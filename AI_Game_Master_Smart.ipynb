{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AI游戏主持人 - Elder Scrolls冒险\n", "\n", "欢迎来到Elder Scrolls世界的AI驱动冒险！这个笔记本将带你体验一个由AI主持的角色扮演游戏。\n", "\n", "## 功能特点\n", "- 智能AI游戏主持人\n", "- 沉浸式Elder Scrolls世界观\n", "- 对话记录保存\n", "- 支持多种模型选择"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的依赖\n", "!pip install transformers torch accelerate bitsandbytes"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import json\n", "import datetime\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "import gc\n", "\n", "class GameMaster:\n", "    def __init__(self):\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.conversation_history = []\n", "        self.game_context = \"\"\"\n", "你是Elder Scrolls世界的游戏主持人。你需要：\n", "1. 创造沉浸式的游戏体验\n", "2. 描述环境、NPC和事件\n", "3. 根据玩家行动推进故事\n", "4. 保持Elder Scrolls的世界观和氛围\n", "\"\"\"\n", "    \n", "    def load_model(self, model_name=\"Qwen/Qwen2.5-3B-Instruct\"):\n", "        \"\"\"加载模型\"\"\"\n", "        print(f\"正在加载模型: {model_name}\")\n", "        \n", "        # 配置量化以节省内存\n", "        quantization_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_compute_dtype=torch.float16\n", "        )\n", "        \n", "        self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "        self.model = AutoModelForCausalLM.from_pretrained(\n", "            model_name,\n", "            quantization_config=quantization_config,\n", "            device_map=\"auto\"\n", "        )\n", "        \n", "        print(\"模型加载完成！\")\n", "    \n", "    def generate_response(self, user_input):\n", "        \"\"\"生成AI回应\"\"\"\n", "        if not self.model:\n", "            return \"请先加载模型！\"\n", "        \n", "        # 构建对话历史\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": self.game_context},\n", "        ]\n", "        \n", "        # 添加历史对话\n", "        for msg in self.conversation_history[-10:]:  # 只保留最近10轮对话\n", "            messages.append(msg)\n", "        \n", "        messages.append({\"role\": \"user\", \"content\": user_input})\n", "        \n", "        # 生成回应\n", "        text = self.tokenizer.apply_chat_template(\n", "            messages,\n", "            tokenize=False,\n", "            add_generation_prompt=True\n", "        )\n", "        \n", "        model_inputs = self.tokenizer([text], return_tensors=\"pt\").to(self.model.device)\n", "        \n", "        with torch.no_grad():\n", "            generated_ids = self.model.generate(\n", "                model_inputs.input_ids,\n", "                max_new_tokens=512,\n", "                do_sample=True,\n", "                temperature=0.7,\n", "                pad_token_id=self.tokenizer.eos_token_id\n", "            )\n", "        \n", "        response = self.tokenizer.batch_decode(\n", "            generated_ids[:, model_inputs.input_ids.shape[-1]:],\n", "            skip_special_tokens=True\n", "        )[0]\n", "        \n", "        # 保存对话\n", "        self.conversation_history.append({\"role\": \"user\", \"content\": user_input})\n", "        self.conversation_history.append({\"role\": \"assistant\", \"content\": response})\n", "        \n", "        return response\n", "    \n", "    def clear_memory(self):\n", "        \"\"\"清理GPU内存\"\"\"\n", "        if self.model:\n", "            del self.model\n", "            del self.tokenizer\n", "            self.model = None\n", "            self.tokenizer = None\n", "        \n", "        torch.cuda.empty_cache()\n", "        gc.collect()\n", "        print(\"内存已清理\")\n", "\n", "# 创建游戏主持人实例\n", "game_master = GameMaster()\n", "print(\"AI游戏主持人已初始化！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def start_adventure(model_name=\"Qwen/Qwen2.5-3B-Instruct\"):\n", "    \"\"\"开始冒险\"\"\"\n", "    game_master.load_model(model_name)\n", "    \n", "    print(\"=\" * 50)\n", "    print(\"欢迎来到Elder Scrolls世界！\")\n", "    print(\"=\" * 50)\n", "    \n", "    # 开场白\n", "    opening = game_master.generate_response(\n", "        \"请为玩家创建一个Elder Scrolls世界的开场场景，描述环境和初始情况。\"\n", "    )\n", "    print(\"\\n游戏主持人：\")\n", "    print(opening)\n", "    \n", "    return \"冒险已开始！在下面的单元格中输入你的行动。\"\n", "\n", "def player_action(action):\n", "    \"\"\"玩家行动\"\"\"\n", "    if not game_master.model:\n", "        return \"请先运行 start_adventure() 开始游戏！\"\n", "    \n", "    print(f\"\\n玩家: {action}\")\n", "    print(\"-\" * 30)\n", "    \n", "    response = game_master.generate_response(action)\n", "    print(\"游戏主持人：\")\n", "    print(response)\n", "    \n", "    return response\n", "\n", "def save_conversation():\n", "    \"\"\"保存对话记录\"\"\"\n", "    timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    filename = f\"elder_scrolls_adventure_{timestamp}.json\"\n", "    \n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(game_master.conversation_history, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"对话记录已保存到: {filename}\")\n", "    return filename\n", "\n", "print(\"游戏功能已加载！\")\n", "print(\"\\n可用命令:\")\n", "print(\"- start_adventure()  # 开始冒险（使用3B模型）\")\n", "print(\"- start_adventure('Qwen/Qwen2.5-7B-Instruct')  # 使用7B模型\")\n", "print(\"- player_action('你的行动')  # 执行玩家行动\")\n", "print(\"- save_conversation()  # 保存对话记录\")\n", "print(\"- game_master.clear_memory()  # 清理GPU内存\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 开始你的冒险！\n", "# 取消下面的注释来开始游戏\n", "\n", "# start_adventure()  # 使用3B模型开始"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 在这里输入你的行动\n", "# 例如: player_action(\"我环顾四周，寻找任何有趣的东西\")\n", "\n", "# player_action(\"你的行动\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}