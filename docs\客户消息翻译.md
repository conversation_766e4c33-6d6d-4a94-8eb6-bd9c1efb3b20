# 客户消息翻译

## Kodaav - 2025年6月3日 上午4:25
**状态：PROMOTED**

你好！我需要一个Python脚本在Google Colab上与LLM一起工作。想法是有一个简单的聊天界面来与模型交互，以及保存、导入和导出选项，包括.json和.doc或类似格式（以这种方式导出的json文件旨在被处理并用作数据集）。我也希望稍后添加一些RAG模块，但这个第一版本还不需要它们。我希望它是模块化的（就像大多数Colab笔记本一样），我们可能稍后会添加更多功能。

我现在想要使用的模型是Llama 70B Instruct（Lorablated）和基于它制作的微调版本。

我可能会在A100 Colab服务器上运行该脚本。请告诉我是否可以做到，以及费用是多少。

这条消息与以下内容相关：
相关项目图片
我将开发自定义python脚本和自动化解决方案

---

## Kodaav - 2025年6月3日 晚上11:30

1. 我在huggingface上有访问权限，我也有.safetensors文件，它应该从我的驱动器文件夹复制到服务器的本地驱动器然后加载。到目前为止我无法让它工作，当它工作时它使用GPU，这使得它无法使用。请确保它使用服务器的GPU而不是在CPU上加载模型。

2. 我更喜欢.docx，因为格式可以稍后调整以创建会话期间发生的事情的叙述。我将在第5点详细说明。

3. 我对你作为专业人士的选择持开放态度，所以请随意制作你认为合适的界面，如果它超出我的期望，可能会为你赢得小费。这个版本将用于早期测试目的，所以我仍然对你的想法持开放态度。如果有帮助的话，我喜欢Abezee（开放的谷歌字体）等字体，我的品牌颜色是黑色和107c10（xbox绿色）。我想象一个受chatgpt启发的界面，但正如我所说，我更信任你的选择而不是我的。

4. 我会尽快提供一个示例JSON供参考，基本上是一个典型的用户/助手结构。

5. 我给你一些背景：这个脚本的目的是测试我的AI游戏主持人的早期版本（本质上是一个单人桌面RPG）。我将使用这个特定的Llama 70B作为基础，并使用游戏会话和RPG数据集进行增量微调。当我们完成这个项目时，我会有更多工作给你，主要是微调我的模型的脚本，我们会从那里看。当模型的第一个工作版本准备好时，我们稍后会开发一个高级GUI。

---

## Kodaav - 2025年6月3日 晚上11:39

关于我之前提到的RAG模块：游戏主持人需要跟踪关键故事事件、角色及其历史和与玩家的互动、派系、库存，可能稍后还有更多东西。现在我想在每个RPG会话期间使用3个文档作为参考：库存、关键事件和玩家偏好。这些应该根据会话期间发生的情况由模型自动更新。我希望这些可以下载并稍后导入。到目前为止我找到的保存/加载以前会话并存储它们的唯一方法就是这个，如果你有任何想法我会很感激。

做这个也要多少钱？我们可以在不到24小时内开始。如果我现在负担不起全部费用，我们下个月做RAG部分，但我宁愿和你一起做所有事情，这样从一开始就有结构。

---

## Kodaav - 2025年6月3日 晚上11:57

我们现在就开始。我得到这个工作还是你更喜欢做一个定制的？

---

## Kodaav - 2025年6月3日 晚上11:58

一旦工作被接受，我就可以直接上传JSON，对吧？

---

## Kodaav - 2025年6月4日 上午12:04

顺便说一下，我真的很感激你对这个项目的兴趣和你的善意，将RAG包含在我们的价格中。我认为我们将有一个长期富有成效的关系，因为我有一些更多的项目可以使用你的技能。

---

## Kodaav - 2025年6月4日 上午12:53

应该都准备好了，我也将JSON上传到了Models文件夹

---

## Kodaav - 2025年6月4日 上午12:34

我已经填写了所有要求，请随时告诉我你的想法，我在这里解决你的所有疑问。

模型链接：https://drive.google.com/drive/folders/1q99V-A1taYM8osdJFq4rg94jLM3HuQ_2?usp=sharing

我驱动器上的空笔记本（也许它让你使用我的Colab+订阅的A100服务器）：https://colab.research.google.com/drive/1eGgJjuOep8qqS2vsARZSljrWjTMFTxnD?usp=sharing

如果你需要更多时间完全没问题，我更喜欢你花时间舒适地做。这只是4个阶段中的第0阶段，所以还有更多工作要来，每一步都会变得更好！
