{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎮 AI游戏主持人 - Elder Scrolls冒险\n", "\n", "欢迎来到Elder Scrolls世界的AI驱动冒险！这个系统为您提供专业的AI游戏主持人体验。\n", "\n", "## ✨ 功能特点\n", "- 🤖 智能AI游戏主持人（支持Llama 70B模型）\n", "- 🌍 沉浸式Elder Scrolls世界观\n", "- 💬 ChatGPT风格聊天界面\n", "- 📁 完整的会话管理（JSON/DOCX导出）\n", "- 📚 RAG文档管理系统\n", "- 🎨 专业品牌设计（Abezee字体 + Xbox绿色配色）\n", "\n", "## 🚀 快速开始\n", "1. 运行下面的安装单元格\n", "2. 配置模型设置\n", "3. 启动聊天界面\n", "4. 开始您的冒险！"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 安装必要的依赖包\n", "!pip install -q transformers torch accelerate bitsandbytes\n", "!pip install -q gradio ipywidgets\n", "!pip install -q python-docx\n", "!pip install -q sentence-transformers\n", "\n", "print(\"✅ 所有依赖包安装完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 导入必要的库\n", "import torch\n", "import json\n", "import datetime\n", "import os\n", "import shutil\n", "import gradio as gr\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "from docx import Document\n", "from docx.shared import Inches\n", "import gc\n", "import uuid\n", "from typing import List, Dict, Any, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 库导入完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ⚙️ 配置设置\n", "class Config:\n", "    \"\"\"项目配置类\"\"\"\n", "    \n", "    # 模型配置\n", "    MODEL_OPTIONS = {\n", "        \"3B_DEV\": {\n", "            \"name\": \"meta-llama/Llama-3.2-3B-Instruct\",\n", "            \"path\": \"/content/drive/MyDrive/AI_Game_Master/models/meta-llama_Llama-3.2-3B-Instruct/\",\n", "            \"description\": \"开发测试用3B模型\"\n", "        },\n", "        \"70B_PROD\": {\n", "            \"name\": \"meta-llama/Llama-2-70b-chat-hf\",\n", "            \"path\": \"/content/drive/MyDrive/AI_Game_Master/models/llama_70b/\",\n", "            \"description\": \"生产环境70B模型\"\n", "        }\n", "    }\n", "    \n", "    # 当前使用的模型（开发阶段使用3B）\n", "    CURRENT_MODEL = \"3B_DEV\"\n", "    \n", "    # 界面配置\n", "    UI_CONFIG = {\n", "        \"theme\": \"dark\",\n", "        \"primary_color\": \"#107c10\",  # Xbox绿色\n", "        \"font_family\": \"Abezee, sans-serif\",\n", "        \"max_history_display\": 50,\n", "        \"auto_scroll\": True\n", "    }\n", "    \n", "    # 游戏设定\n", "    GAME_CONFIG = {\n", "        \"world\": \"Elder Scrolls\",\n", "        \"style\": \"High Fantasy\",\n", "        \"tone\": \"Mythic & Gritty\",\n", "        \"max_context_length\": 4000,\n", "        \"temperature\": 0.7,\n", "        \"max_new_tokens\": 512\n", "    }\n", "    \n", "    # 文件路径\n", "    PATHS = {\n", "        \"sessions\": \"./sessions/\",\n", "        \"exports\": \"./exports/\",\n", "        \"rag_docs\": \"./rag_documents/\"\n", "    }\n", "\n", "# 创建必要的目录\n", "for path in Config.PATHS.values():\n", "    os.makedirs(path, exist_ok=True)\n", "\n", "print(\"✅ 配置初始化完成！\")\n", "print(f\"当前模型: {Config.MODEL_OPTIONS[Config.CURRENT_MODEL]['description']}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🧠 模型管理器\n", "class ModelManager:\n", "    \"\"\"AI模型加载和管理\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.model_name = None\n", "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "        \n", "    def load_model(self, model_key: str = None) -> bool:\n", "        \"\"\"加载指定的模型\"\"\"\n", "        if model_key is None:\n", "            model_key = Config.CURRENT_MODEL\n", "            \n", "        model_config = Config.MODEL_OPTIONS[model_key]\n", "        model_name = model_config[\"name\"]\n", "        model_path = model_config[\"path\"]\n", "        \n", "        try:\n", "            print(f\"🔄 正在加载模型: {model_config['description']}\")\n", "            \n", "            # 检查Google Drive中是否有本地模型文件\n", "            if os.path.exists(model_path) and model_key == \"3B_DEV\":\n", "                print(f\"📁 从Google Drive加载: {model_path}\")\n", "                model_name = model_path\n", "            \n", "            # 配置量化以节省内存\n", "            quantization_config = BitsAndBytesConfig(\n", "                load_in_4bit=True,\n", "                bnb_4bit_compute_dtype=torch.float16,\n", "                bnb_4bit_quant_type=\"nf4\",\n", "                bnb_4bit_use_double_quant=True\n", "            )\n", "            \n", "            # 加载tokenizer\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\n", "                model_name,\n", "                trust_remote_code=True\n", "            )\n", "            \n", "            # 设置pad_token\n", "            if self.tokenizer.pad_token is None:\n", "                self.tokenizer.pad_token = self.tokenizer.eos_token\n", "            \n", "            # 加载模型\n", "            self.model = AutoModelForCausalLM.from_pretrained(\n", "                model_name,\n", "                quantization_config=quantization_config,\n", "                device_map=\"auto\",\n", "                trust_remote_code=True,\n", "                torch_dtype=torch.float16\n", "            )\n", "            \n", "            self.model_name = model_name\n", "            print(f\"✅ 模型加载成功！设备: {self.device}\")\n", "            print(f\"💾 GPU内存使用: {torch.cuda.memory_allocated()/1024**3:.2f}GB\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 模型加载失败: {str(e)}\")\n", "            return False\n", "    \n", "    def generate_response(self, messages: List[Dict], **kwargs) -> str:\n", "        \"\"\"生成AI回应\"\"\"\n", "        if not self.model or not self.tokenizer:\n", "            return \"❌ 模型未加载，请先运行模型加载单元格！\"\n", "        \n", "        try:\n", "            # 应用聊天模板\n", "            text = self.tokenizer.apply_chat_template(\n", "                messages,\n", "                tokenize=False,\n", "                add_generation_prompt=True\n", "            )\n", "            \n", "            # 编码输入\n", "            model_inputs = self.tokenizer(\n", "                [text], \n", "                return_tensors=\"pt\",\n", "                padding=True,\n", "                truncation=True,\n", "                max_length=Config.GAME_CONFIG[\"max_context_length\"]\n", "            ).to(self.device)\n", "            \n", "            # 生成回应\n", "            with torch.no_grad():\n", "                generated_ids = self.model.generate(\n", "                    model_inputs.input_ids,\n", "                    max_new_tokens=kwargs.get(\"max_new_tokens\", Config.GAME_CONFIG[\"max_new_tokens\"]),\n", "                    temperature=kwargs.get(\"temperature\", Config.GAME_CONFIG[\"temperature\"]),\n", "                    do_sample=True,\n", "                    pad_token_id=self.tokenizer.eos_token_id,\n", "                    eos_token_id=self.tokenizer.eos_token_id\n", "                )\n", "            \n", "            # 解码回应\n", "            response = self.tokenizer.batch_decode(\n", "                generated_ids[:, model_inputs.input_ids.shape[-1]:],\n", "                skip_special_tokens=True\n", "            )[0]\n", "            \n", "            return response.strip()\n", "            \n", "        except Exception as e:\n", "            return f\"❌ 生成回应时出错: {str(e)}\"\n", "    \n", "    def clear_memory(self):\n", "        \"\"\"清理GPU内存\"\"\"\n", "        if self.model:\n", "            del self.model\n", "            del self.tokenizer\n", "            self.model = None\n", "            self.tokenizer = None\n", "            self.model_name = None\n", "        \n", "        if torch.cuda.is_available():\n", "            torch.cuda.empty_cache()\n", "        gc.collect()\n", "        print(\"🧹 GPU内存已清理\")\n", "\n", "# 创建全局模型管理器实例\n", "model_manager = ModelManager()\n", "print(\"✅ 模型管理器初始化完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📚 会话管理器\n", "class SessionManager:\n", "    \"\"\"会话数据管理和持久化\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.current_session = {\n", "            \"session_id\": str(uuid.uuid4()),\n", "            \"created_at\": datetime.datetime.now().isoformat(),\n", "            \"conversation\": [],\n", "            \"metadata\": {\n", "                \"world\": Config.GAME_CONFIG[\"world\"],\n", "                \"style\": Config.GAME_CONFIG[\"style\"],\n", "                \"tone\": Config.GAME_CONFIG[\"tone\"]\n", "            }\n", "        }\n", "        self.system_prompt = self._create_system_prompt()\n", "    \n", "    def _create_system_prompt(self) -> str:\n", "        \"\"\"创建系统提示词\"\"\"\n", "        return f\"\"\"\n", "你是Elder Scrolls世界的专业游戏主持人。你的任务是：\n", "\n", "🎭 角色设定：\n", "- 你是一位经验丰富的游戏主持人\n", "- 深谙Elder Scrolls世界观和传说\n", "- 擅长创造沉浸式的游戏体验\n", "\n", "🌍 世界观要求：\n", "- 严格遵循Elder Scrolls官方设定\n", "- 风格：{Config.GAME_CONFIG['style']}\n", "- 语调：{Config.GAME_CONFIG['tone']}\n", "- 包含丰富的环境描述和NPC互动\n", "\n", "🎮 游戏机制：\n", "- 支持复杂的RPG系统（战斗、技能、物品管理）\n", "- 提供有意义的选择和后果\n", "- 维护游戏连续性和角色发展\n", "- 适时进行技能检定和战斗判定\n", "\n", "📝 叙述风格：\n", "- 使用生动的描述性语言\n", "- 平衡对话、行动和环境描述\n", "- 保持神话感和史诗感\n", "- 适当的紧张感和戏剧冲突\n", "\n", "请开始你的游戏主持，创造一个难忘的Elder Scrolls冒险！\n", "\"\"\"\n", "    \n", "    def add_message(self, role: str, content: str, metadata: Dict = None) -> None:\n", "        \"\"\"添加消息到当前会话\"\"\"\n", "        message = {\n", "            \"role\": role,\n", "            \"content\": content,\n", "            \"timestamp\": datetime.datetime.now().isoformat()\n", "        }\n", "        \n", "        if metadata:\n", "            message[\"metadata\"] = metadata\n", "            \n", "        self.current_session[\"conversation\"].append(message)\n", "    \n", "    def get_messages_for_model(self, max_history: int = 10) -> List[Dict]:\n", "        \"\"\"获取用于模型的消息格式\"\"\"\n", "        messages = [{\"role\": \"system\", \"content\": self.system_prompt}]\n", "        \n", "        # 获取最近的对话历史\n", "        recent_conversation = self.current_session[\"conversation\"][-max_history:]\n", "        \n", "        for msg in recent_conversation:\n", "            messages.append({\n", "                \"role\": msg[\"role\"],\n", "                \"content\": msg[\"content\"]\n", "            })\n", "        \n", "        return messages\n", "    \n", "    def save_session(self, filename: str = None) -> str:\n", "        \"\"\"保存当前会话到JSON文件\"\"\"\n", "        if filename is None:\n", "            timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filename = f\"elder_scrolls_session_{timestamp}.json\"\n", "        \n", "        filepath = os.path.join(Config.PATHS[\"sessions\"], filename)\n", "        \n", "        # 转换为客户要求的格式\n", "        export_data = {\n", "            \"conversation\": [\n", "                {\"role\": msg[\"role\"], \"content\": msg[\"content\"]}\n", "                for msg in self.current_session[\"conversation\"]\n", "            ]\n", "        }\n", "        \n", "        with open(filepath, 'w', encoding='utf-8') as f:\n", "            json.dump(export_data, f, ensure_ascii=False, indent=2)\n", "        \n", "        return filepath\n", "    \n", "    def load_session(self, filepath: str) -> bool:\n", "        \"\"\"从JSON文件加载会话\"\"\"\n", "        try:\n", "            with open(filepath, 'r', encoding='utf-8') as f:\n", "                data = json.load(f)\n", "            \n", "            # 重置当前会话\n", "            self.current_session = {\n", "                \"session_id\": str(uuid.uuid4()),\n", "                \"created_at\": datetime.datetime.now().isoformat(),\n", "                \"conversation\": [],\n", "                \"metadata\": {\n", "                    \"world\": Config.GAME_CONFIG[\"world\"],\n", "                    \"style\": Config.GAME_CONFIG[\"style\"],\n", "                    \"tone\": Config.GAME_CONFIG[\"tone\"],\n", "                    \"loaded_from\": filepath\n", "                }\n", "            }\n", "            \n", "            # 加载对话数据\n", "            if \"conversation\" in data:\n", "                for msg in data[\"conversation\"]:\n", "                    self.add_message(msg[\"role\"], msg[\"content\"])\n", "            \n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 加载会话失败: {str(e)}\")\n", "            return False\n", "    \n", "    def export_to_docx(self, filename: str = None) -> str:\n", "        \"\"\"导出会话到DOCX文档\"\"\"\n", "        if filename is None:\n", "            timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filename = f\"elder_scrolls_adventure_{timestamp}.docx\"\n", "        \n", "        filepath = os.path.join(Config.PATHS[\"exports\"], filename)\n", "        \n", "        # 创建文档\n", "        doc = Document()\n", "        \n", "        # 添加标题\n", "        title = doc.add_heading('Elder Scrolls AI冒险记录', 0)\n", "        \n", "        # 添加会话信息\n", "        doc.add_heading('会话信息', level=1)\n", "        info_para = doc.add_paragraph()\n", "        info_para.add_run(f\"会话ID: {self.current_session['session_id']}\\n\")\n", "        info_para.add_run(f\"创建时间: {self.current_session['created_at']}\\n\")\n", "        info_para.add_run(f\"消息数量: {len(self.current_session['conversation'])}\\n\")\n", "        \n", "        # 添加对话内容\n", "        doc.add_heading('冒险记录', level=1)\n", "        \n", "        for i, msg in enumerate(self.current_session[\"conversation\"], 1):\n", "            role_name = \"🎮 玩家\" if msg[\"role\"] == \"user\" else \"🎭 游戏主持人\"\n", "            \n", "            # 添加角色标题\n", "            role_para = doc.add_paragraph()\n", "            role_run = role_para.add_run(f\"{role_name} (#{i})\")\n", "            role_run.bold = True\n", "            \n", "            # 添加消息内容\n", "            content_para = doc.add_paragraph(msg[\"content\"])\n", "            content_para.style = 'Quote'\n", "            \n", "            # 添加分隔线\n", "            if i < len(self.current_session[\"conversation\"]):\n", "                doc.add_paragraph(\"─\" * 50)\n", "        \n", "        # 保存文档\n", "        doc.save(filepath)\n", "        return filepath\n", "    \n", "    def get_conversation_summary(self) -> Dict:\n", "        \"\"\"获取会话摘要信息\"\"\"\n", "        conversation = self.current_session[\"conversation\"]\n", "        return {\n", "            \"total_messages\": len(conversation),\n", "            \"user_messages\": len([m for m in conversation if m[\"role\"] == \"user\"]),\n", "            \"assistant_messages\": len([m for m in conversation if m[\"role\"] == \"assistant\"]),\n", "            \"session_id\": self.current_session[\"session_id\"],\n", "            \"created_at\": self.current_session[\"created_at\"]\n", "        }\n", "\n", "# 创建全局会话管理器实例\n", "session_manager = SessionManager()\n", "print(\"✅ 会话管理器初始化完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📖 RAG文档管理系统\n", "class RAGDocumentManager:\n", "    \"\"\"RAG文档管理系统 - 管理库存、关键事件、玩家偏好\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.documents = {\n", "            \"inventory\": {\n", "                \"items\": [],\n", "                \"gold\": 0,\n", "                \"last_updated\": datetime.datetime.now().isoformat()\n", "            },\n", "            \"key_events\": [],\n", "            \"player_preferences\": {\n", "                \"playstyle\": \"未知\",\n", "                \"choices\": [],\n", "                \"personality_traits\": [],\n", "                \"preferred_solutions\": []\n", "            }\n", "        }\n", "        self.auto_update_enabled = True\n", "    \n", "    def update_inventory(self, action: str, item: str = None, quantity: int = 1, gold_change: int = 0) -> None:\n", "        \"\"\"更新库存信息\"\"\"\n", "        inventory = self.documents[\"inventory\"]\n", "        \n", "        if action == \"add\" and item:\n", "            # 查找现有物品\n", "            existing_item = next((i for i in inventory[\"items\"] if i[\"name\"] == item), None)\n", "            if existing_item:\n", "                existing_item[\"quantity\"] += quantity\n", "            else:\n", "                inventory[\"items\"].append({\n", "                    \"name\": item,\n", "                    \"quantity\": quantity,\n", "                    \"acquired_at\": datetime.datetime.now().isoformat()\n", "                })\n", "        \n", "        elif action == \"remove\" and item:\n", "            # 移除物品的逻辑\n", "            updated_items = []\n", "            for i in inventory[\"items\"]:\n", "                if i[\"name\"] == item:\n", "                    i[\"quantity\"] -= quantity\n", "                    if i[\"quantity\"] > 0:\n", "                        updated_items.append(i)\n", "                else:\n", "                    updated_items.append(i)\n", "            inventory[\"items\"] = updated_items\n", "        \n", "        if gold_change != 0:\n", "            inventory[\"gold\"] = max(0, inventory[\"gold\"] + gold_change)\n", "        \n", "        inventory[\"last_updated\"] = datetime.datetime.now().isoformat()\n", "    \n", "    def add_key_event(self, event: str, importance: str = \"medium\", location: str = None) -> None:\n", "        \"\"\"添加关键事件\"\"\"\n", "        event_data = {\n", "            \"event\": event,\n", "            \"timestamp\": datetime.datetime.now().isoformat(),\n", "            \"importance\": importance,  # high, medium, low\n", "            \"location\": location\n", "        }\n", "        \n", "        self.documents[\"key_events\"].append(event_data)\n", "        \n", "        # 保持事件数量在合理范围内\n", "        if len(self.documents[\"key_events\"]) > 50:\n", "            # 优先保留重要事件\n", "            self.documents[\"key_events\"] = sorted(\n", "                self.documents[\"key_events\"],\n", "                key=lambda x: {\"high\": 3, \"medium\": 2, \"low\": 1}[x[\"importance\"]],\n", "                reverse=True\n", "            )[:50]\n", "    \n", "    def update_player_preferences(self, choice: str = None, trait: str = None, solution: str = None) -> None:\n", "        \"\"\"更新玩家偏好\"\"\"\n", "        prefs = self.documents[\"player_preferences\"]\n", "        \n", "        if choice:\n", "            prefs[\"choices\"].append({\n", "                \"choice\": choice,\n", "                \"timestamp\": datetime.datetime.now().isoformat()\n", "            })\n", "            \n", "            # 保持选择历史在合理范围内\n", "            if len(prefs[\"choices\"]) > 30:\n", "                prefs[\"choices\"] = prefs[\"choices\"][-30:]\n", "        \n", "        if trait and trait not in prefs[\"personality_traits\"]:\n", "            prefs[\"personality_traits\"].append(trait)\n", "        \n", "        if solution and solution not in prefs[\"preferred_solutions\"]:\n", "            prefs[\"preferred_solutions\"].append(solution)\n", "    \n", "    def analyze_message_for_updates(self, role: str, content: str) -> None:\n", "        \"\"\"分析消息内容并自动更新文档\"\"\"\n", "        if not self.auto_update_enabled:\n", "            return\n", "        \n", "        content_lower = content.lower()\n", "        \n", "        # 检测金币变化\n", "        if \"金币\" in content_lower or \"金子\" in content_lower:\n", "            import re\n", "            gold_pattern = r'(\\d+)\\s*金币'\n", "            matches = re.findall(gold_pattern, content)\n", "            if matches:\n", "                gold_amount = int(matches[0])\n", "                if any(word in content_lower for word in [\"获得\", \"得到\", \"赚到\"]):\n", "                    self.update_inventory(\"add\", gold_change=gold_amount)\n", "                elif any(word in content_lower for word in [\"失去\", \"花费\", \"支付\"]):\n", "                    self.update_inventory(\"remove\", gold_change=-gold_amount)\n", "        \n", "        # 检测重要事件\n", "        event_keywords = [\"战斗\", \"对话\", \"任务\", \"发现\", \"到达\", \"离开\", \"死亡\", \"胜利\", \"失败\"]\n", "        if any(keyword in content_lower for keyword in event_keywords):\n", "            # 简化的事件检测\n", "            if len(content) > 50:  # 只记录较长的重要内容\n", "                importance = \"high\" if any(word in content_lower for word in [\"死亡\", \"胜利\", \"重要\", \"关键\"]) else \"medium\"\n", "                self.add_key_event(content[:100] + \"...\" if len(content) > 100 else content, importance)\n", "        \n", "        # 检测玩家偏好（基于用户消息）\n", "        if role == \"user\":\n", "            # 检测解决问题的方式\n", "            if any(word in content_lower for word in [\"战斗\", \"攻击\", \"杀死\"]):\n", "                self.update_player_preferences(solution=\"战斗解决\")\n", "            elif any(word in content_lower for word in [\"对话\", \"说服\", \"谈判\"]):\n", "                self.update_player_preferences(solution=\"对话解决\")\n", "            elif any(word in content_lower for word in [\"潜行\", \"偷偷\", \"避开\"]):\n", "                self.update_player_preferences(solution=\"潜行解决\")\n", "            \n", "            # 记录玩家选择\n", "            if len(content) < 200:  # 记录较短的选择性内容\n", "                self.update_player_preferences(choice=content)\n", "    \n", "    def get_context_for_ai(self) -> str:\n", "        \"\"\"获取用于AI的上下文信息\"\"\"\n", "        context_parts = []\n", "        \n", "        # 库存信息\n", "        inventory = self.documents[\"inventory\"]\n", "        if inventory[\"items\"] or inventory[\"gold\"] > 0:\n", "            context_parts.append(\"📦 当前库存:\")\n", "            if inventory[\"gold\"] > 0:\n", "                context_parts.append(f\"- 金币: {inventory['gold']}\")\n", "            for item in inventory[\"items\"][:10]:  # 只显示前10个物品\n", "                context_parts.append(f\"- {item['name']} x{item['quantity']}\")\n", "        \n", "        # 关键事件\n", "        recent_events = sorted(\n", "            self.documents[\"key_events\"],\n", "            key=lambda x: x[\"timestamp\"],\n", "            reverse=True\n", "        )[:5]  # 最近5个事件\n", "        \n", "        if recent_events:\n", "            context_parts.append(\"\\n📜 最近重要事件:\")\n", "            for event in recent_events:\n", "                context_parts.append(f\"- {event['event']}\")\n", "        \n", "        # 玩家偏好\n", "        prefs = self.documents[\"player_preferences\"]\n", "        if prefs[\"preferred_solutions\"]:\n", "            context_parts.append(f\"\\n🎯 玩家偏好解决方式: {', '.join(prefs['preferred_solutions'][-3:])}\")\n", "        \n", "        return \"\\n\".join(context_parts) if context_parts else \"\"\n", "    \n", "    def save_documents(self, filename: str = None) -> str:\n", "        \"\"\"保存RAG文档到文件\"\"\"\n", "        if filename is None:\n", "            timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filename = f\"rag_documents_{timestamp}.json\"\n", "        \n", "        filepath = os.path.join(Config.PATHS[\"rag_docs\"], filename)\n", "        \n", "        with open(filepath, 'w', encoding='utf-8') as f:\n", "            json.dump(self.documents, f, ensure_ascii=False, indent=2)\n", "        \n", "        return filepath\n", "    \n", "    def load_documents(self, filepath: str) -> bool:\n", "        \"\"\"从文件加载RAG文档\"\"\"\n", "        try:\n", "            with open(filepath, 'r', encoding='utf-8') as f:\n", "                self.documents = json.load(f)\n", "            return True\n", "        except Exception as e:\n", "            print(f\"❌ 加载RAG文档失败: {str(e)}\")\n", "            return False\n", "    \n", "    def get_summary(self) -> Dict:\n", "        \"\"\"获取文档摘要\"\"\"\n", "        return {\n", "            \"inventory_items\": len(self.documents[\"inventory\"][\"items\"]),\n", "            \"gold\": self.documents[\"inventory\"][\"gold\"],\n", "            \"key_events\": len(self.documents[\"key_events\"]),\n", "            \"player_choices\": len(self.documents[\"player_preferences\"][\"choices\"]),\n", "            \"preferred_solutions\": len(self.documents[\"player_preferences\"][\"preferred_solutions\"])\n", "        }\n", "\n", "# 创建全局RAG文档管理器实例\n", "rag_manager = RAGDocumentManager()\n", "print(\"✅ RAG文档管理器初始化完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎨 聊天界面样式\n", "CHAT_CSS = \"\"\"\n", "<style>\n", "@import url('https://fonts.googleapis.com/css2?family=ABeeZee:ital,wght@0,400;1,400&display=swap');\n", "\n", ".chat-container {\n", "    font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif;\n", "    background-color: #000000;\n", "    color: #ffffff;\n", "    border-radius: 10px;\n", "    padding: 20px;\n", "    margin: 10px 0;\n", "}\n", "\n", ".user-message {\n", "    background-color: #1a1a1a;\n", "    border-left: 4px solid #107c10;\n", "    padding: 15px;\n", "    margin: 10px 0;\n", "    border-radius: 5px;\n", "}\n", "\n", ".assistant-message {\n", "    background-color: #0d1117;\n", "    border-left: 4px solid #107c10;\n", "    padding: 15px;\n", "    margin: 10px 0;\n", "    border-radius: 5px;\n", "}\n", "\n", ".message-header {\n", "    color: #107c10;\n", "    font-weight: bold;\n", "    margin-bottom: 8px;\n", "    font-size: 14px;\n", "}\n", "\n", ".message-content {\n", "    line-height: 1.6;\n", "    white-space: pre-wrap;\n", "}\n", "\n", ".system-info {\n", "    background-color: #107c10;\n", "    color: #ffffff;\n", "    padding: 10px;\n", "    border-radius: 5px;\n", "    margin: 10px 0;\n", "    font-size: 12px;\n", "}\n", "\n", ".rag-context {\n", "    background-color: #2d2d2d;\n", "    border: 1px solid #107c10;\n", "    padding: 10px;\n", "    border-radius: 5px;\n", "    margin: 10px 0;\n", "    font-size: 12px;\n", "    color: #cccccc;\n", "}\n", "</style>\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 💬 ChatGPT风格聊天界面（已在下方实现）\n", "print(\"✅ 聊天界面组件准备完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 模型加载\n", "\n", "运行下面的单元格来加载AI模型。开发阶段使用3B模型，生产环境可切换到70B模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔄 加载AI模型\n", "print(\"🔄 开始加载AI模型...\")\n", "print(f\"📍 当前配置: {Config.MODEL_OPTIONS[Config.CURRENT_MODEL]['description']}\")\n", "\n", "# 挂载Google Drive（如果在Colab中）\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "    print(\"✅ Google Drive已挂载\")\n", "except ImportError:\n", "    print(\"ℹ️ 非Colab环境，跳过Drive挂载\")\n", "\n", "# 加载模型\n", "success = model_manager.load_model(Config.CURRENT_MODEL)\n", "\n", "if success:\n", "    print(\"\\n🎉 模型加载成功！\")\n", "    print(\"📊 系统状态:\")\n", "    print(f\"  - 模型: {model_manager.model_name}\")\n", "    print(f\"  - 设备: {model_manager.device}\")\n", "    if torch.cuda.is_available():\n", "        print(f\"  - GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB / {torch.cuda.memory_reserved()/1024**3:.2f}GB\")\n", "    \n", "    # 初始化游戏\n", "    chat_interface.add_system_message(\"🎮 AI游戏主持人已准备就绪！\")\n", "    chat_interface.add_system_message(\"🌍 欢迎来到Elder Scrolls世界！\")\n", "    \n", "    # 生成开场白\n", "    opening_messages = session_manager.get_messages_for_model(max_history=0)\n", "    opening_messages.append({\n", "        \"role\": \"user\", \n", "        \"content\": \"请为玩家创建一个Elder Scrolls世界的开场场景，描述环境和初始情况。请保持神话感和史诗感，让玩家感受到这个世界的魅力。\"\n", "    })\n", "    \n", "    opening_response = model_manager.generate_response(opening_messages)\n", "    session_manager.add_message(\"assistant\", opening_response)\n", "    chat_interface.add_message_to_display(\"assistant\", opening_response)\n", "    \n", "    print(\"\\n✨ 游戏已开始！请滚动到下方使用聊天界面。\")\n", "else:\n", "    print(\"\\n❌ 模型加载失败！请检查配置和网络连接。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 聊天界面\n", "\n", "使用下面的界面与AI游戏主持人进行互动。界面采用ChatGPT风格设计，支持完整的对话历史显示。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎮 启动聊天界面\n", "def create_chat_interface():\n", "    \"\"\"创建Gradio聊天界面\"\"\"\n", "    \n", "    def chat_fn(message, history):\n", "        \"\"\"处理聊天消息\"\"\"\n", "        if not message.strip():\n", "            return \"\", history\n", "        \n", "        # 处理用户输入\n", "        chat_interface.process_user_input(message)\n", "        \n", "        # 更新历史记录\n", "        history.append([message, session_manager.current_session[\"conversation\"][-1][\"content\"]])\n", "        \n", "        return \"\", history\n", "    \n", "    def get_session_info():\n", "        \"\"\"获取会话信息\"\"\"\n", "        session_summary = session_manager.get_conversation_summary()\n", "        rag_summary = rag_manager.get_summary()\n", "        \n", "        info = f\"\"\"\n", "📊 **会话统计**\n", "- 总消息数: {session_summary['total_messages']}\n", "- 玩家消息: {session_summary['user_messages']}\n", "- AI回应: {session_summary['assistant_messages']}\n", "- 会话ID: {session_summary['session_id'][:8]}...\n", "\n", "📚 **游戏状态**\n", "- 库存物品: {rag_summary['inventory_items']}\n", "- 金币: {rag_summary['gold']}\n", "- 关键事件: {rag_summary['key_events']}\n", "- 玩家选择: {rag_summary['player_choices']}\n", "- 偏好方案: {rag_summary['preferred_solutions']}\n", "        \"\"\"\n", "        return info\n", "    \n", "    def save_session_fn():\n", "        \"\"\"保存会话\"\"\"\n", "        try:\n", "            json_path = session_manager.save_session()\n", "            docx_path = session_manager.export_to_docx()\n", "            rag_path = rag_manager.save_documents()\n", "            \n", "            return f\"✅ 保存成功！\\n📁 JSON: {json_path}\\n📄 DOCX: {docx_path}\\n📚 RAG: {rag_path}\"\n", "        except Exception as e:\n", "            return f\"❌ 保存失败: {str(e)}\"\n", "    \n", "    def clear_session_fn():\n", "        \"\"\"清空会话\"\"\"\n", "        session_manager.__init__()\n", "        rag_manager.__init__()\n", "        chat_interface.clear_history()\n", "        return \"✅ 会话已清空\", []\n", "    \n", "    # 创建Gradio界面\n", "    try:\n", "        # 尝试使用新版本的主题\n", "        theme = gr.themes.Dark()\n", "    except AttributeError:\n", "        try:\n", "            # 尝试使用旧版本的主题\n", "            theme = \"dark\"\n", "        except:\n", "            # 如果都不行，使用默认主题\n", "            theme = None\n", "    \n", "    with gr.<PERSON><PERSON>(title=\"AI游戏主持人 - Elder Scrolls冒险\", theme=theme) as demo:\n", "        gr.HTM<PERSON>(\"\"\"\n", "        <div style=\"text-align: center; padding: 20px; background: linear-gradient(90deg, #000000, #107c10, #000000); color: white; font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif;\">\n", "            <h1>🎮 AI游戏主持人 - Elder Scrolls冒险</h1>\n", "            <p>沉浸式AI驱动的角色扮演游戏体验</p>\n", "        </div>\n", "        \"\"\")\n", "        \n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>(scale=3):\n", "                chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                    label=\"🎭 游戏对话\",\n", "                    height=500,\n", "                    show_label=True,\n", "                    container=True,\n", "                    bubble_full_width=False\n", "                )\n", "                \n", "                with gr.<PERSON>():\n", "                    msg = gr.Textbox(\n", "                        label=\"💬 您的行动\",\n", "                        placeholder=\"描述您的行动或对话...\",\n", "                        lines=2,\n", "                        scale=4\n", "                    )\n", "                    send_btn = gr.<PERSON><PERSON>(\"🚀 发送\", variant=\"primary\", scale=1)\n", "            \n", "            with gr.<PERSON>(scale=1):\n", "                gr.HTML(\"<h3>🎛️ 控制面板</h3>\")\n", "                \n", "                session_info = gr.Textbox(\n", "                    label=\"📊 会话信息\",\n", "                    value=get_session_info(),\n", "                    lines=10,\n", "                    interactive=False\n", "                )\n", "                \n", "                refresh_btn = gr.<PERSON><PERSON>(\"🔄 刷新信息\", variant=\"secondary\")\n", "                save_btn = gr.<PERSON><PERSON>(\"💾 保存会话\", variant=\"primary\")\n", "                clear_btn = gr.<PERSON><PERSON>(\"🗑️ 清空会话\", variant=\"stop\")\n", "                \n", "                save_status = gr.Textbox(\n", "                    label=\"💾 保存状态\",\n", "                    lines=3,\n", "                    interactive=False\n", "                )\n", "        \n", "        # 事件绑定\n", "        msg.submit(chat_fn, [msg, chatbot], [msg, chatbot])\n", "        send_btn.click(chat_fn, [msg, chatbot], [msg, chatbot])\n", "        refresh_btn.click(get_session_info, outputs=session_info)\n", "        save_btn.click(save_session_fn, outputs=save_status)\n", "        clear_btn.click(clear_session_fn, outputs=[save_status, chatbot])\n", "    \n", "    return demo\n", "\n", "# 启动ChatGPT风格界面\n", "if model_manager.model:\n", "    print(\"🚀 启动ChatGPT风格聊天界面...\")\n", "    \n", "    def create_chatgpt_interface():\n", "        \"\"\"创建真正的ChatGPT风格界面\"\"\"\n", "        \n", "        def respond(message, history):\n", "            \"\"\"处理用户消息并返回AI回应\"\"\"\n", "            if not message.strip():\n", "                return \"\", history\n", "            \n", "            try:\n", "                # 添加用户消息到会话\n", "                session_manager.add_message(\"user\", message)\n", "                rag_manager.analyze_message_for_updates(\"user\", message)\n", "                \n", "                # 获取AI回应\n", "                messages = session_manager.get_messages_for_model()\n", "                rag_context = rag_manager.get_context_for_ai()\n", "                \n", "                if rag_context:\n", "                    enhanced_content = f\"{message}\\n\\n{rag_context}\"\n", "                    messages[-1][\"content\"] = enhanced_content\n", "                \n", "                response = model_manager.generate_response(messages)\n", "                \n", "                # 添加AI回应到会话\n", "                session_manager.add_message(\"assistant\", response)\n", "                rag_manager.analyze_message_for_updates(\"assistant\", response)\n", "                \n", "                # 更新历史记录\n", "                history.append([message, response])\n", "                \n", "                return \"\", history\n", "                \n", "            except Exception as e:\n", "                error_msg = f\"❌ 处理消息时出错: {str(e)}\"\n", "                history.append([message, error_msg])\n", "                return \"\", history\n", "        \n", "        # 自定义CSS样式 - 真正的ChatGPT风格\n", "        css = \"\"\"\n", "        @import url('https://fonts.googleapis.com/css2?family=ABeeZee:ital,wght@0,400;1,400&display=swap');\n", "        \n", "        .gradio-container {\n", "            font-family: '<PERSON><PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, sans-serif !important;\n", "            background: #000000 !important;\n", "        }\n", "        \n", "        .chat-message {\n", "            font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif !important;\n", "        }\n", "        \n", "        /* 主标题样式 */\n", "        .main-header {\n", "            background: linear-gradient(135deg, #000000 0%, #107c10 50%, #000000 100%);\n", "            color: white;\n", "            text-align: center;\n", "            padding: 20px;\n", "            border-radius: 10px;\n", "            margin-bottom: 20px;\n", "            font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif;\n", "        }\n", "        \n", "        /* 聊天界面样式 */\n", "        .chatbot {\n", "            background: #1a1a1a !important;\n", "            border: 1px solid #107c10 !important;\n", "            border-radius: 10px !important;\n", "        }\n", "        \n", "        /* 用户消息样式 */\n", "        .user-message {\n", "            background: #107c10 !important;\n", "            color: white !important;\n", "            border-radius: 18px !important;\n", "            padding: 10px 15px !important;\n", "            margin: 5px 0 !important;\n", "            max-width: 80% !important;\n", "            margin-left: auto !important;\n", "        }\n", "        \n", "        /* AI消息样式 */\n", "        .bot-message {\n", "            background: #2d2d2d !important;\n", "            color: white !important;\n", "            border: 1px solid #107c10 !important;\n", "            border-radius: 18px !important;\n", "            padding: 10px 15px !important;\n", "            margin: 5px 0 !important;\n", "            max-width: 80% !important;\n", "        }\n", "        \n", "        /* 输入框样式 */\n", "        .input-box {\n", "            background: #1a1a1a !important;\n", "            border: 2px solid #107c10 !important;\n", "            border-radius: 25px !important;\n", "            color: white !important;\n", "            padding: 12px 20px !important;\n", "            font-family: '<PERSON><PERSON><PERSON><PERSON>', sans-serif !important;\n", "        }\n", "        \n", "        /* 发送按钮样式 */\n", "        .send-button {\n", "            background: #107c10 !important;\n", "            border: none !important;\n", "            border-radius: 50% !important;\n", "            color: white !important;\n", "            width: 50px !important;\n", "            height: 50px !important;\n", "            font-size: 18px !important;\n", "        }\n", "        \n", "        /* 侧边栏样式 */\n", "        .sidebar {\n", "            background: #1a1a1a !important;\n", "            border: 1px solid #107c10 !important;\n", "            border-radius: 10px !important;\n", "            padding: 15px !important;\n", "            color: white !important;\n", "        }\n", "        \"\"\"\n", "        \n", "        # 创建界面\n", "        with gr.Blocks(css=css, title=\"AI游戏主持人 - Elder Scrolls冒险\") as demo:\n", "            # 主标题\n", "            gr.HTM<PERSON>(\"\"\"\n", "            <div class=\"main-header\">\n", "                <h1>🎮 AI游戏主持人 - Elder Scrolls冒险</h1>\n", "                <p>沉浸式AI驱动的角色扮演游戏体验</p>\n", "            </div>\n", "            \"\"\")\n", "            \n", "            with gr.<PERSON>():\n", "                with gr.<PERSON>(scale=4):\n", "                    # ChatGPT风格的聊天界面\n", "                    chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                        [],\n", "                        elem_id=\"chatbot\",\n", "                        bubble_full_width=False,\n", "                        height=600,\n", "                        show_copy_button=True,\n", "                        layout=\"panel\",\n", "                        elem_classes=[\"chatbot\"]\n", "                    )\n", "                    \n", "                    with gr.<PERSON>():\n", "                        msg = gr.Textbox(\n", "                            placeholder=\"描述您的行动或对话...\",\n", "                            container=False,\n", "                            scale=7,\n", "                            elem_classes=[\"input-box\"]\n", "                        )\n", "                        submit = gr.<PERSON><PERSON>(\n", "                            \"🚀\",\n", "                            scale=1,\n", "                            elem_classes=[\"send-button\"]\n", "                        )\n", "                \n", "                with gr.<PERSON>(scale=1):\n", "                    gr.HTML('<div class=\"sidebar\"><h3>🎛️ 游戏控制</h3></div>')\n", "                    \n", "                    # 游戏状态显示\n", "                    status_display = gr.Textbox(\n", "                        label=\"📊 游戏状态\",\n", "                        lines=8,\n", "                        interactive=False,\n", "                        elem_classes=[\"sidebar\"]\n", "                    )\n", "                    \n", "                    # 控制按钮\n", "                    refresh_btn = gr.<PERSON><PERSON>(\"🔄 刷新状态\", elem_classes=[\"sidebar\"])\n", "                    save_btn = gr.<PERSON><PERSON>(\"💾 保存游戏\", elem_classes=[\"sidebar\"])\n", "                    export_btn = gr.<PERSON><PERSON>(\"📄 导出DOCX\", elem_classes=[\"sidebar\"])\n", "                    clear_btn = gr.<PERSON><PERSON>(\"🗑️ 清空对话\", elem_classes=[\"sidebar\"])\n", "                    \n", "                    # 状态信息\n", "                    info_display = gr.Textbox(\n", "                        label=\"ℹ️ 操作结果\",\n", "                        lines=3,\n", "                        interactive=False,\n", "                        elem_classes=[\"sidebar\"]\n", "                    )\n", "            \n", "            # 事件绑定\n", "            msg.submit(respond, [msg, chatbot], [msg, chatbot])\n", "            submit.click(respond, [msg, chatbot], [msg, chatbot])\n", "            \n", "            # 功能按钮事件\n", "            def update_status():\n", "                session_summary = session_manager.get_conversation_summary()\n", "                rag_summary = rag_manager.get_summary()\n", "                rag_context = rag_manager.get_context_for_ai()\n", "                \n", "                status = f\"\"\"\n", "📊 会话统计:\n", "• 总消息: {session_summary['total_messages']}\n", "• 玩家消息: {session_summary['user_messages']}\n", "• AI回应: {session_summary['assistant_messages']}\n", "\n", "🎮 游戏状态:\n", "• 库存物品: {rag_summary['inventory_items']}\n", "• 金币: {rag_summary['gold']}\n", "• 关键事件: {rag_summary['key_events']}\n", "• 玩家选择: {rag_summary['player_choices']}\n", "\n", "{rag_context[:200] + '...' if len(rag_context) > 200 else rag_context}\n", "                \"\"\".strip()\n", "                return status\n", "            \n", "            def save_game():\n", "                try:\n", "                    json_path = session_manager.save_session()\n", "                    return f\"✅ 游戏已保存\\n📁 {json_path}\"\n", "                except Exception as e:\n", "                    return f\"❌ 保存失败: {str(e)}\"\n", "            \n", "            def export_docx():\n", "                try:\n", "                    docx_path = session_manager.export_to_docx()\n", "                    return f\"✅ DOCX已导出\\n📄 {docx_path}\"\n", "                except Exception as e:\n", "                    return f\"❌ 导出失败: {str(e)}\"\n", "            \n", "            def clear_chat():\n", "                session_manager.__init__()\n", "                rag_manager.__init__()\n", "                return \"✅ 对话已清空\", []\n", "            \n", "            refresh_btn.click(update_status, outputs=status_display)\n", "            save_btn.click(save_game, outputs=info_display)\n", "            export_btn.click(export_docx, outputs=info_display)\n", "            clear_btn.click(clear_chat, outputs=[info_display, chatbot])\n", "            \n", "            # 初始化状态显示\n", "            demo.load(update_status, outputs=status_display)\n", "        \n", "        return demo\n", "    \n", "    try:\n", "        demo = create_chatgpt_interface()\n", "        demo.launch(\n", "            share=True,\n", "            debug=False,\n", "            show_error=True,\n", "            inbrowser=True\n", "        )\n", "    except Exception as e:\n", "        print(f\"❌ 界面启动失败: {str(e)}\")\n", "        print(\"请检查Gradio版本和依赖包\")\n", "else:\n", "    print(\"❌ 请先加载模型！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🛠️ 工具函数\n", "\n", "以下是一些有用的工具函数，用于管理会话、导入导出数据等。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔧 工具函数\n", "\n", "def load_example_session():\n", "    \"\"\"加载客户提供的示例会话\"\"\"\n", "    example_path = \"/content/drive/MyDrive/AI_Game_Master/docs/Ursus Werebear Awakens.json\"\n", "    \n", "    if os.path.exists(example_path):\n", "        success = session_manager.load_session(example_path)\n", "        if success:\n", "            print(\"✅ 示例会话加载成功！\")\n", "            print(f\"📊 加载了 {len(session_manager.current_session['conversation'])} 条消息\")\n", "            \n", "            # 重新构建聊天界面显示\n", "            chat_interface.clear_history()\n", "            for msg in session_manager.current_session['conversation']:\n", "                chat_interface.add_message_to_display(msg['role'], msg['content'])\n", "            \n", "            return \"✅ 示例会话已加载\"\n", "        else:\n", "            return \"❌ 加载示例会话失败\"\n", "    else:\n", "        return \"❌ 找不到示例文件\"\n", "\n", "def switch_model(model_key: str):\n", "    \"\"\"切换模型\"\"\"\n", "    if model_key not in Config.MODEL_OPTIONS:\n", "        return f\"❌ 无效的模型选项: {model_key}\"\n", "    \n", "    # 清理当前模型\n", "    model_manager.clear_memory()\n", "    \n", "    # 更新配置\n", "    Config.CURRENT_MODEL = model_key\n", "    \n", "    # 加载新模型\n", "    success = model_manager.load_model(model_key)\n", "    \n", "    if success:\n", "        return f\"✅ 已切换到: {Config.MODEL_OPTIONS[model_key]['description']}\"\n", "    else:\n", "        return f\"❌ 切换模型失败: {model_key}\"\n", "\n", "def get_system_status():\n", "    \"\"\"获取系统状态\"\"\"\n", "    status = {\n", "        \"model_loaded\": model_manager.model is not None,\n", "        \"current_model\": Config.CURRENT_MODEL,\n", "        \"device\": model_manager.device,\n", "        \"session_messages\": len(session_manager.current_session['conversation']),\n", "        \"rag_items\": len(rag_manager.documents['inventory']['items']),\n", "        \"rag_events\": len(rag_manager.documents['key_events'])\n", "    }\n", "    \n", "    if torch.cuda.is_available():\n", "        status[\"gpu_memory_allocated\"] = f\"{torch.cuda.memory_allocated()/1024**3:.2f}GB\"\n", "        status[\"gpu_memory_reserved\"] = f\"{torch.cuda.memory_reserved()/1024**3:.2f}GB\"\n", "    \n", "    return status\n", "\n", "def export_all_data(prefix: str = None):\n", "    \"\"\"导出所有数据\"\"\"\n", "    if prefix is None:\n", "        prefix = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    results = {}\n", "    \n", "    try:\n", "        # 导出会话JSON\n", "        json_path = session_manager.save_session(f\"{prefix}_session.json\")\n", "        results[\"json\"] = json_path\n", "        \n", "        # 导出DOCX\n", "        docx_path = session_manager.export_to_docx(f\"{prefix}_adventure.docx\")\n", "        results[\"docx\"] = docx_path\n", "        \n", "        # 导出RAG文档\n", "        rag_path = rag_manager.save_documents(f\"{prefix}_rag.json\")\n", "        results[\"rag\"] = rag_path\n", "        \n", "        return f\"✅ 全部导出成功！\\n\" + \"\\n\".join([f\"{k}: {v}\" for k, v in results.items()])\n", "        \n", "    except Exception as e:\n", "        return f\"❌ 导出失败: {str(e)}\"\n", "\n", "def copy_to_google_drive():\n", "    \"\"\"复制文件到Google Drive\"\"\"\n", "    try:\n", "        drive_path = \"/content/drive/MyDrive/AI_Game_Master/\"\n", "        \n", "        # 复制会话文件\n", "        for folder in [\"sessions\", \"exports\", \"rag_documents\"]:\n", "            src_folder = Config.PATHS[folder]\n", "            dst_folder = os.path.join(drive_path, folder)\n", "            \n", "            os.makedirs(dst_folder, exist_ok=True)\n", "            \n", "            for file in os.listdir(src_folder):\n", "                src_file = os.path.join(src_folder, file)\n", "                dst_file = os.path.join(dst_folder, file)\n", "                shutil.copy2(src_file, dst_file)\n", "        \n", "        return \"✅ 文件已复制到Google Drive\"\n", "        \n", "    except Exception as e:\n", "        return f\"❌ 复制失败: {str(e)}\"\n", "\n", "# 显示可用命令\n", "print(\"🛠️ 可用工具函数:\")\n", "print(\"- load_example_session()     # 加载示例会话\")\n", "print(\"- switch_model('70B_PROD')   # 切换到70B模型\")\n", "print(\"- get_system_status()        # 获取系统状态\")\n", "print(\"- export_all_data()          # 导出所有数据\")\n", "print(\"- copy_to_google_drive()     # 复制到Google Drive\")\n", "print(\"- model_manager.clear_memory() # 清理GPU内存\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📖 使用说明\n", "\n", "### 🚀 快速开始\n", "1. **安装依赖**: 运行第一个单元格安装所需包\n", "2. **初始化系统**: 运行配置和管理器初始化单元格\n", "3. **加载模型**: 运行模型加载单元格\n", "4. **开始游戏**: 使用聊天界面与AI游戏主持人互动\n", "\n", "### 🎮 游戏功能\n", "- **沉浸式对话**: AI会根据Elder Scrolls世界观创造丰富的游戏体验\n", "- **智能记忆**: 系统会自动跟踪您的库存、重要事件和偏好\n", "- **多格式导出**: 支持JSON和DOCX格式导出游戏记录\n", "- **会话管理**: 可以保存和加载游戏进度\n", "\n", "### 🔧 高级功能\n", "- **模型切换**: 开发时使用3B模型，生产时切换到70B模型\n", "- **RAG系统**: 自动管理游戏状态和玩家偏好\n", "- **数据持久化**: 所有数据可保存到Google Drive\n", "\n", "### 📁 文件结构\n", "```\n", "AI_Game_Master/\n", "├── sessions/          # 会话JSON文件\n", "├── exports/           # DOCX导出文件\n", "├── rag_documents/     # RAG文档数据\n", "└── docs/              # 项目文档\n", "```\n", "\n", "### 🎯 客户需求实现状态\n", "- ✅ Llama 70B模型支持（可切换）\n", "- ✅ ChatGPT风格聊天界面\n", "- ✅ Abezee字体 + Xbox绿色配色\n", "- ✅ JSON/DOCX导出功能\n", "- ✅ 会话管理系统\n", "- ✅ RAG文档管理（库存、事件、偏好）\n", "- ✅ Elder Scrolls世界观集成\n", "- ✅ Google Drive集成\n", "\n", "### 🆘 故障排除\n", "- **模型加载失败**: 检查Google Drive挂载和模型文件路径\n", "- **GPU内存不足**: 运行 `model_manager.clear_memory()` 清理内存\n", "- **界面无响应**: 重新运行聊天界面单元格\n", "- **文件保存失败**: 检查文件权限和磁盘空间\n", "\n", "---\n", "\n", "**🎉 享受您的Elder Scrolls AI冒险！**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}