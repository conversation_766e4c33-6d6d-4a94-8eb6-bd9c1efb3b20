Kodaav

Jun 03, 2025, 4:25 AM
PROMOTED


Hi! I need a Python script to work with LLMs on Google Colab. The idea would be to have a simple chat interface to interact with the model, as well as save, import and export options that include .json and .doc or similar format (json files exported this way are intended to be processed and used as datasets). I'd love to add some RAG modules as well later on, but this first version doesn't need them yet. I'd like it to be modular (as most Colab notebooks are) and we'll probably add more functionalities later.
The models I'm looking to work with right now are Llama 70B Instruct (Lorablated) and fine tunes made out of it.
I'll probably run that script on an A100 Colab server. Please let me know if it could be done and how much it would cost

This message relates to:

Related item image
I will develop custom python scripts and automation solutions

S
Profile Image
Me

Jun 03, 2025, 9:07 AM

Hi, sorry for my late reply!

Thank you for your detailed requirements. I can help you build the Python script for Google Colab as described, including the chat interface, save/import/export features (json and doc format), and modular code structure. My quote for the first version is $80.

Before we get started, could you please clarify a few points:
1. Model access: Do you already have access to the Llama 70B Instruct (Lorablated) model in a format that can be loaded directly on Colab (e.g., via Hugging Face, or a specific repo/checkpoint), or do you need help with model setup as well?
2. .doc format: For exporting/importing .doc files, do you prefer Microsoft Word (.docx), plain text (.txt), or another format?
3. Interface preference: Would you like a simple text box in the notebook (using ipywidgets), or are you open to using other tools (such as Streamlit, Gradio, etc.) for the chat interface?
4. Dataset structure: When exporting to JSON files, do you have a preferred structure or schema for the dataset?
5. Other requirements: Is there anything else you want to include in this first version?

Let me know your thoughts!

K
Profile Image
Kodaav

Jun 03, 2025, 11:30 PM


1. I got access on huggingface and I also have the .safetensors file, it should be copied from my drive folder to the server's local drive and then loaded. So far I couldn't get it to work, and when it did it used GPU which makes it unusable. Please make sure it's using the server's GPU and not load the model on its CPU.
2. I'd prefer .docx since formatting can be adjusted later to create a narration of what happens during the sessions. I'll elaborate that at 5.
3.  I'm open to your choice as a professional, so feel free to make the interface as you see fit, it may earn you a tip if it surpasses my expectations. This version will be for early testing purposes, so I'm still open to your ideas. If it helps, I like fonts such as Abezee (open google font) and my brand colors are black and 107c10 (xbox green). I imagined an interface inspired on chatgpt, but as I said I trust your choice more than mine here.
4. I'll provide a sample JSON for reference as soon as we get started, it's basically a typical user/assistant structure.

5. I'll give you some context: The purpose of this script is to test early versions of my AI Game Master (Esentially a single player tabletop RPG). I'm gonna use this particular Llama 70B as a foundation and do incremental fine-tuning with game sessions and RPG datasets. When we finish this project I'll have more work for you, mainly the script to fine-tune my model and we'll see from there. We'll work on an advanced GUI later, when the first working version of the model is ready.

K
Profile Image
Kodaav

Jun 03, 2025, 11:39 PM


About the RAG modules I mentioned before: Game Master needs to keep track of key story events, characters and their history and interactions with the player, factions, inventory and probably more stuff later on. For now I want to use 3 documents for reference during every RPG session: Inventory, Key Events and Player Preferences. These should be updated by the model automatically depending on what's happening during the session. I want these to be downloaded and imported later if possible. The only way I found so far to save/load previous sessions and store them is this one, if you got any ideas I'll appreciate them.

How much would it be to do this as well? We can start in less than 24h. If I can't afford all of it right now we'll do the RAG part next month, but I'd rather do everything with you so it's all structured from the beginning.

S
Profile Image
Me

Jun 03, 2025, 11:49 PM

Hi Kodaav,

Thank you for your detailed feedback and for sharing more about the RAG requirements. I fully understand your needs, and I'm genuinely excited to be part of your AI Game Master project!

1. Model Loading  
I'll ensure the script can copy your Llama 70B model file from your Google Drive to the Colab server's local storage, and load it using the server's GPU (CUDA)—not the CPU. I'll focus on thoroughly testing and confirming this part.

2. .docx Export  
I'll use the .docx format for exporting each session, making it easy for you to edit and narrate session details later.

3. Interface Design  
I'll design a clean, user-friendly chat interface inspired by ChatGPT, using the Abezee font and your brand colors (black and #107c10). I'll apply my professional judgment to optimize the experience and keep you updated with previews as I progress.

4. Data Format  
When convenient, please send me a sample JSON structure so I can make sure the import/export functionality fully matches your needs.

5. RAG Document Management  
Thank you for providing more details on the RAG module. I understand you want the Game Master to automatically manage and update three session reference documents (Inventory, Key Events, and Player Preferences), and allow these documents to be downloaded and imported for future use or review.  
I am very interested in this project, so I will not charge any extra fee for the RAG-related document management features. However, for a smooth development process and clear structure, I suggest we first complete the main workflow (chat interface, basic save/import/export, and core features), which I estimate will take about 3 days for the initial version. Once the main workflow is stable, I'll proceed to add the RAG document management and integration features—at no additional cost.

6. Future Collaboration  
I'm also looking forward to working with you on future tasks, such as fine-tuning scripts and developing an advanced GUI!

S
Profile Image
Me

Jun 03, 2025, 11:49 PM

Before we get started, I'd like to confirm a few details:
- Approximately how large is your Llama 70B model file? (This will help ensure Colab's storage and GPU memory are sufficient.)
- For the chat interface, would you like the full conversation history to be visible and scrollable, or just the latest exchange?
- For each chat session, do you have a preferred maximum length (such as number of messages or tokens) to keep in memory?
- If you have any specific preferences for the structure or format of the reference documents, please let me know. If not, I can propose a flexible and extensible format for you to review.

Development plan:  
- I will first complete the main workflow, with a quote of $80 USD and an estimated delivery time of around 3 days for the initial version.
- After the main workflow is ready, I'll proceed to add the RAG document management features as discussed, with no extra charge.
- If you have any other ideas or requirements, please feel free to share them at any time.

Thank you again for your trust. I look forward to your reply!

S
Profile Image
Me

Jun 03, 2025, 11:51 PM

To proceed smoothly with development and testing, could you please provide me with the Llama 70B Instruct (LoRA fine-tuned) model file or the download method (such as a Hugging Face link or Google Drive share link)?
If there are any special access permissions or tokens required, please let me know as well. This will allow me to test model loading and inference on Colab in advance and help ensure a smooth workflow.

K
Profile Image
Kodaav

Jun 03, 2025, 11:57 PM


We're starting right now. Do I get this gig or do you prefer to make a custom one?

K
Profile Image
Kodaav

Jun 03, 2025, 11:58 PM


I can upload the JSON directly once the job is accepted, right?

S
Profile Image
Me

Jun 03, 2025, 11:59 PM

I recommend we use a custom offer/contract for this project, so we can clearly define all the requirements, delivery timeline, and milestones. This will help us both keep things organized and make it easier to manage any future updates or additional features.
﻿
I’ll send you a custom offer right away, including all the details we’ve discussed. Please let me know if you’d like any adjustments!

S
Profile Image
Me

Jun 04, 2025, 12:04 AM

Here's your custom offer

$80
I will develop custom python scripts and automation solutions
I will develop a Python Colab script for your AI Game Master project, including:
﻿
- Colab setup and testing of your Llama 70B Instruct (LoRA fine-tuned) model with GPU support.
- A clean, user-friendly chat interface (Abezee font, your brand colors), supporting import/export of conversations and .docx export.
- Core session management: save, load, and export conversations.
- Clean, modular code structure for easy future upgrades.
- Ongoing communication and preview updates.
﻿
Note: RAG document management (automatic tracking and updating of Inventory, Key Events, and Player Preferences) will be added for free in a follow-up after the main workflow is delivered.

Read more
Your offer includes
2 Revisions
3 Days Delivery
Install script
Test script
Revisions
View order
K
Profile Image
Kodaav

Jun 04, 2025, 12:04 AM


By the way, I really appreciate your interest in this project and your kindness by including the RAG in our price. I think we're gonna have a long fruitful relationship since I have some more projects that could use your skills.

S
Profile Image
Me

Jun 04, 2025, 12:06 AM

Thank you so much for your kind words, Kodaav!
I’m truly excited to be working with you on this project, and I really appreciate your trust and openness. I’m committed to delivering high-quality work and making sure everything runs smoothly for you.
﻿
I’m also looking forward to a long-term collaboration and would be happy to help with your future projects as well. If you have any ideas or needs down the road, feel free to reach out anytime!
﻿
Let’s make this project a great success together!

K
Profile Image
Kodaav

Jun 04, 2025, 12:53 AM


It should be all ready, I also uploaded the JSON to the Models folder

Thank you so much for accepting the offer, Kodaav!
I’ve received your confirmation and I’m excited to get started.
﻿
It’s currently past midnight in my time zone, so I’ll get some rest now and will be back online in the morning to begin work.
Whenever you’re ready, please upload the JSON file and any other necessary resources. Once I have those, I’ll start right away.
﻿
Thanks again, and looking forward to working with you!

ssgydss
You sent a message
Jun 4, 2025, 12:16 AM

Reminder: Please fill out the required information by clicking Provide Requirements above. If you have any questions, I will be happy to help.

The order started
Jun 4, 2025, 12:16 AM

Your delivery date was updated to June 7
Jun 4, 2025, 12:16 AM

kodaav
kodaav sent you a message
Jun 4, 2025, 12:34 AM

I already filled all requirements, please let me know your thoughts at any time, I'm here to solve all of your doubts.

Model link: https://drive.google.com/drive/folders/1q99V-A1taYM8osdJFq4rg94jLM3HuQ_2?usp=sharing

Empty notebook on my drive (maybe it lets you use the A100 server with my Colab+ subscription): https://colab.research.google.com/drive/1eGgJjuOep8qqS2vsARZSljrWjTMFTxnD?usp=sharing

If you need more time there's no problem at all, I prefer if you take your time and do it comfortably. This is only Phase 0 out of 4, so there's more work coming and it keeps getting better with each step!

ssgydss
You sent a message
Jun 4, 2025, 11:39 AM

Thanks for sharing all the files and links, Kodaav.

I’ve received the model, JSON, and Colab notebook, and I’m starting to set things up now. If I have any questions, I’ll let you know.

Thanks for your flexibility!😄