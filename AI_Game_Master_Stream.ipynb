{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎮 AI游戏主持人 - 流式输出版本\n", "\n", "这是一个支持流式输出的ChatGPT风格AI游戏主持人。模型会逐字生成回应，提供更真实的对话体验。\n", "\n", "## ✨ 特色功能\n", "- 🔄 **真正的流式输出** - 像ChatGPT一样逐字显示\n", "- 💬 **ChatGPT风格界面** - 现代化的聊天体验\n", "- 🎯 **智能游戏状态追踪** - 自动记录物品、金币、位置等\n", "- 📚 **RAG文档管理** - 智能上下文管理\n", "- 💾 **完整的保存功能** - JSON和DOCX格式导出\n", "\n", "## 🚀 快速开始\n", "1. 运行\"安装依赖\"单元格\n", "2. 运行\"初始化系统\"单元格  \n", "3. 运行\"加载模型\"单元格\n", "4. 运行\"启动界面\"单元格\n", "5. 开始您的Elder Scrolls冒险！"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 安装依赖包\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package):\n", "    subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "\n", "packages = [\n", "    \"torch\",\n", "    \"transformers>=4.36.0\", \n", "    \"accelerate\",\n", "    \"bitsandbytes\",\n", "    \"gradio>=4.0.0\",\n", "    \"python-docx\",\n", "    \"numpy\"\n", "]\n", "\n", "print(\"🔄 安装依赖包...\")\n", "for package in packages:\n", "    try:\n", "        install_package(package)\n", "        print(f\"✅ {package} 安装成功\")\n", "    except Exception as e:\n", "        print(f\"❌ {package} 安装失败: {e}\")\n", "\n", "print(\"\\n🎉 依赖包安装完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📚 导入库和配置\n", "import torch\n", "import json\n", "import datetime\n", "import uuid\n", "import os\n", "import gradio as gr\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "from docx import Document\n", "import gc\n", "from typing import List, Dict, Optional, Generator\n", "import re\n", "import time\n", "\n", "print(\"✅ 库导入成功！\")\n", "\n", "# 🔧 配置\n", "class Config:\n", "    MODEL_OPTIONS = {\n", "        \"3b\": {\n", "            \"name\": \"microsoft/DialoGPT-medium\",\n", "            \"description\": \"3B参数模型 - 开发测试用\"\n", "        },\n", "        \"70b\": {\n", "            \"name\": \"meta-llama/Llama-2-70b-chat-hf\", \n", "            \"description\": \"70B参数模型 - 生产环境\"\n", "        }\n", "    }\n", "    \n", "    CURRENT_MODEL = \"3b\"\n", "    \n", "    GAME_CONFIG = {\n", "        \"max_context_length\": 4096,\n", "        \"max_new_tokens\": 512,\n", "        \"temperature\": 0.8,\n", "        \"top_p\": 0.9\n", "    }\n", "    \n", "    SYSTEM_PROMPT = \"\"\"\n", "你是一位专业的Elder Scrolls世界AI游戏主持人。创造沉浸式的角色扮演体验。\n", "\n", "核心原则：\n", "1. 保持Elder Scrolls世界观一致性\n", "2. 创造引人入胜的故事情节\n", "3. 给予玩家有意义的选择\n", "4. 描述生动的场景和角色\n", "5. 保持游戏平衡性和挑战性\n", "\n", "回应格式：\n", "- 使用生动描述性语言\n", "- 提供多个行动选项\n", "- 保持神秘感和史诗感\n", "- 适当使用Elder Scrolls术语\n", "\"\"\"\n", "\n", "print(\"✅ 配置初始化完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🤖 支持流式输出的模型管理器\n", "class StreamingModelManager:\n", "    def __init__(self):\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "        self.model_name = None\n", "        print(f\"🔧 流式模型管理器初始化，设备: {self.device}\")\n", "    \n", "    def load_model(self, model_key: str) -> bool:\n", "        if model_key not in Config.MODEL_OPTIONS:\n", "            print(f\"❌ 未知模型: {model_key}\")\n", "            return False\n", "        \n", "        model_config = Config.MODEL_OPTIONS[model_key]\n", "        model_name = model_config[\"name\"]\n", "        \n", "        try:\n", "            print(f\"🔄 加载模型: {model_name}\")\n", "            \n", "            # 清理之前的模型\n", "            if self.model is not None:\n", "                del self.model\n", "                del self.tokenizer\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "            \n", "            # 量化配置\n", "            quantization_config = BitsAndBytesConfig(\n", "                load_in_4bit=True,\n", "                bnb_4bit_compute_dtype=torch.float16,\n", "                bnb_4bit_use_double_quant=True,\n", "                bnb_4bit_quant_type=\"nf4\"\n", "            )\n", "            \n", "            # 加载tokenizer\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\n", "                model_name,\n", "                trust_remote_code=True\n", "            )\n", "            \n", "            if self.tokenizer.pad_token is None:\n", "                self.tokenizer.pad_token = self.tokenizer.eos_token\n", "            \n", "            # 加载模型\n", "            self.model = AutoModelForCausalLM.from_pretrained(\n", "                model_name,\n", "                quantization_config=quantization_config,\n", "                device_map=\"auto\",\n", "                trust_remote_code=True,\n", "                torch_dtype=torch.float16\n", "            )\n", "            \n", "            self.model_name = model_name\n", "            print(f\"✅ 模型加载成功！\")\n", "            if torch.cuda.is_available():\n", "                print(f\"💾 GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 模型加载失败: {str(e)}\")\n", "            return False\n", "    \n", "    def generate_response_stream(self, messages: List[Dict], **kwargs) -> Generator[str, None, None]:\n", "        \"\"\"流式生成AI回应\"\"\"\n", "        if not self.model or not self.tokenizer:\n", "            yield \"❌ 模型未加载！\"\n", "            return\n", "        \n", "        try:\n", "            # 构建输入文本\n", "            input_text = \"\"\n", "            for msg in messages:\n", "                role = \"Human\" if msg[\"role\"] == \"user\" else \"Assistant\"\n", "                input_text += f\"{role}: {msg['content']}\\n\"\n", "            input_text += \"Assistant:\"\n", "            \n", "            # 编码输入\n", "            inputs = self.tokenizer.encode(input_text, return_tensors=\"pt\").to(self.device)\n", "            \n", "            # 流式生成参数\n", "            max_new_tokens = kwargs.get(\"max_new_tokens\", Config.GAME_CONFIG[\"max_new_tokens\"])\n", "            temperature = kwargs.get(\"temperature\", Config.GAME_CONFIG[\"temperature\"])\n", "            \n", "            generated_text = \"\"\n", "            \n", "            with torch.no_grad():\n", "                for i in range(max_new_tokens):\n", "                    # 生成下一个token\n", "                    outputs = self.model(inputs)\n", "                    logits = outputs.logits[0, -1, :] / temperature\n", "                    probs = torch.softmax(logits, dim=-1)\n", "                    next_token = torch.multinomial(probs, num_samples=1)\n", "                    \n", "                    # 检查结束条件\n", "                    if next_token.item() == self.tokenizer.eos_token_id:\n", "                        break\n", "                    \n", "                    # 解码新token\n", "                    new_text = self.tokenizer.decode([next_token.item()], skip_special_tokens=True)\n", "                    generated_text += new_text\n", "                    \n", "                    # 更新输入\n", "                    inputs = torch.cat([inputs, next_token.unsqueeze(0)], dim=-1)\n", "                    \n", "                    # 流式输出\n", "                    yield generated_text\n", "                    \n", "                    # 添加小延迟以模拟真实的打字效果\n", "                    time.sleep(0.02)\n", "            \n", "        except Exception as e:\n", "            yield f\"❌ 生成失败: {str(e)}\"\n", "    \n", "    def generate_response(self, messages: List[Dict], **kwargs) -> str:\n", "        \"\"\"非流式生成（兼容性）\"\"\"\n", "        full_response = \"\"\n", "        for partial_response in self.generate_response_stream(messages, **kwargs):\n", "            full_response = partial_response\n", "        return full_response\n", "\n", "# 创建全局模型管理器\n", "model_manager = StreamingModelManager()\n", "print(\"✅ 流式模型管理器创建完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 💾 简化的会话管理器\n", "class SimpleSessionManager:\n", "    def __init__(self):\n", "        self.session = {\n", "            \"id\": str(uuid.uuid4()),\n", "            \"created\": datetime.datetime.now().isoformat(),\n", "            \"messages\": []\n", "        }\n", "        self.add_message(\"system\", Config.SYSTEM_PROMPT)\n", "        print(\"✅ 会话管理器初始化完成！\")\n", "    \n", "    def add_message(self, role: str, content: str):\n", "        self.session[\"messages\"].append({\n", "            \"role\": role,\n", "            \"content\": content,\n", "            \"timestamp\": datetime.datetime.now().isoformat()\n", "        })\n", "    \n", "    def get_messages_for_model(self, max_history: int = 10) -> List[Dict]:\n", "        messages = []\n", "        all_messages = self.session[\"messages\"]\n", "        \n", "        # 系统提示词\n", "        system_msg = next((msg for msg in all_messages if msg[\"role\"] == \"system\"), None)\n", "        if system_msg:\n", "            messages.append({\"role\": \"system\", \"content\": system_msg[\"content\"]})\n", "        \n", "        # 最近对话\n", "        recent = [msg for msg in all_messages if msg[\"role\"] in [\"user\", \"assistant\"]][-max_history:]\n", "        for msg in recent:\n", "            messages.append({\"role\": msg[\"role\"], \"content\": msg[\"content\"]})\n", "        \n", "        return messages\n", "    \n", "    def get_summary(self) -> Dict:\n", "        all_messages = self.session[\"messages\"]\n", "        return {\n", "            \"total\": len(all_messages),\n", "            \"user\": len([m for m in all_messages if m[\"role\"] == \"user\"]),\n", "            \"assistant\": len([m for m in all_messages if m[\"role\"] == \"assistant\"])\n", "        }\n", "    \n", "    def save_session(self) -> str:\n", "        timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        filepath = f\"stream_session_{timestamp}.json\"\n", "        try:\n", "            with open(filepath, 'w', encoding='utf-8') as f:\n", "                json.dump(self.session, f, ensure_ascii=False, indent=2)\n", "            return filepath\n", "        except Exception as e:\n", "            return f\"保存失败: {e}\"\n", "\n", "# 🎮 简化的游戏状态管理器\n", "class SimpleGameState:\n", "    def __init__(self):\n", "        self.state = {\n", "            \"inventory\": [],\n", "            \"gold\": 0,\n", "            \"location\": \"\",\n", "            \"events\": []\n", "        }\n", "        print(\"✅ 游戏状态管理器初始化完成！\")\n", "    \n", "    def analyze_message(self, role: str, content: str):\n", "        \"\"\"简单的消息分析\"\"\"\n", "        content_lower = content.lower()\n", "        \n", "        # 检测物品\n", "        if '获得' in content or '得到' in content:\n", "            # 简单的物品提取\n", "            words = content.split()\n", "            for i, word in enumerate(words):\n", "                if word in ['获得', '得到'] and i + 1 < len(words):\n", "                    item = words[i + 1]\n", "                    if item not in self.state[\"inventory\"]:\n", "                        self.state[\"inventory\"].append(item)\n", "        \n", "        # 检测金币\n", "        gold_match = re.search(r'(\\d+).*金币', content)\n", "        if gold_match:\n", "            gold_amount = int(gold_match.group(1))\n", "            if '获得' in content:\n", "                self.state[\"gold\"] += gold_amount\n", "        \n", "        # 记录事件\n", "        if role == \"assistant\" and len(content) > 50:\n", "            event = content[:100] + \"...\" if len(content) > 100 else content\n", "            self.state[\"events\"].append(event)\n", "            if len(self.state[\"events\"]) > 10:\n", "                self.state[\"events\"] = self.state[\"events\"][-10:]\n", "    \n", "    def get_context(self) -> str:\n", "        context = []\n", "        if self.state[\"location\"]:\n", "            context.append(f\"位置: {self.state['location']}\")\n", "        if self.state[\"gold\"] > 0:\n", "            context.append(f\"金币: {self.state['gold']}\")\n", "        if self.state[\"inventory\"]:\n", "            items = \", \".join(self.state[\"inventory\"][-5:])\n", "            context.append(f\"物品: {items}\")\n", "        return \"\\n\".join(context)\n", "    \n", "    def get_summary(self) -> Dict:\n", "        return {\n", "            \"items\": len(self.state[\"inventory\"]),\n", "            \"gold\": self.state[\"gold\"],\n", "            \"events\": len(self.state[\"events\"])\n", "        }\n", "\n", "# 创建全局管理器\n", "session_manager = SimpleSessionManager()\n", "game_state = SimpleGameState()\n", "print(\"✅ 所有管理器创建完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 模型加载\n", "\n", "运行下面的单元格加载AI模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔄 加载AI模型\n", "print(\"🔄 开始加载AI模型...\")\n", "print(f\"📍 当前配置: {Config.MODEL_OPTIONS[Config.CURRENT_MODEL]['description']}\")\n", "\n", "# 挂载Google Drive（如果在Colab中）\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "    print(\"✅ Google Drive已挂载\")\n", "except ImportError:\n", "    print(\"ℹ️ 非Colab环境，跳过Drive挂载\")\n", "\n", "# 加载模型\n", "success = model_manager.load_model(Config.CURRENT_MODEL)\n", "\n", "if success:\n", "    print(\"\\n🎉 模型加载成功！\")\n", "    print(\"📊 系统状态:\")\n", "    print(f\"  - 模型: {model_manager.model_name}\")\n", "    print(f\"  - 设备: {model_manager.device}\")\n", "    if torch.cuda.is_available():\n", "        print(f\"  - GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB\")\n", "    \n", "    print(\"\\n🎮 AI游戏主持人已准备就绪！\")\n", "    print(\"🌍 欢迎来到Elder Scrolls世界！\")\n", "    \n", "    # 生成开场白\n", "    opening_messages = session_manager.get_messages_for_model(max_history=0)\n", "    opening_messages.append({\n", "        \"role\": \"user\", \n", "        \"content\": \"请为玩家创建一个Elder Scrolls世界的开场场景，描述环境和初始情况。请保持神话感和史诗感。\"\n", "    })\n", "    \n", "    opening_response = model_manager.generate_response(opening_messages)\n", "    session_manager.add_message(\"assistant\", opening_response)\n", "    print(f\"\\n🎭 开场白: {opening_response}\")\n", "    \n", "    print(\"\\n✨ 游戏已开始！请运行下方的聊天界面。\")\n", "else:\n", "    print(\"\\n❌ 模型加载失败！请检查配置和网络连接。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 流式聊天界面\n", "\n", "运行下面的单元格启动支持流式输出的ChatGPT风格界面。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎮 流式ChatGPT界面\n", "def create_streaming_interface():\n", "    \"\"\"创建支持流式输出的ChatGPT风格界面\"\"\"\n", "    \n", "    def respond_stream(message, history):\n", "        \"\"\"流式响应函数\"\"\"\n", "        if not message.strip():\n", "            return history, \"\"\n", "        \n", "        try:\n", "            # 添加用户消息\n", "            session_manager.add_message(\"user\", message)\n", "            game_state.analyze_message(\"user\", message)\n", "            \n", "            # 获取上下文\n", "            messages = session_manager.get_messages_for_model()\n", "            context = game_state.get_context()\n", "            \n", "            if context:\n", "                enhanced_content = f\"{message}\\n\\n游戏状态:\\n{context}\"\n", "                messages[-1][\"content\"] = enhanced_content\n", "            \n", "            # 添加用户消息到历史\n", "            history.append([message, \"\"])\n", "            \n", "            # 流式生成AI回应\n", "            full_response = \"\"\n", "            for partial_response in model_manager.generate_response_stream(messages):\n", "                full_response = partial_response\n", "                # 更新历史记录中的AI回应\n", "                history[-1][1] = full_response\n", "                yield history, \"\"\n", "            \n", "            # 保存完整回应\n", "            session_manager.add_message(\"assistant\", full_response)\n", "            game_state.analyze_message(\"assistant\", full_response)\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"❌ 处理消息时出错: {str(e)}\"\n", "            if history and len(history) > 0:\n", "                history[-1][1] = error_msg\n", "            else:\n", "                history.append([message, error_msg])\n", "            yield history, \"\"\n", "    \n", "    # 简化的CSS样式\n", "    css = \"\"\"\n", "    .gradio-container {\n", "        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;\n", "        background: #0a0a0a !important;\n", "        color: #ffffff !important;\n", "    }\n", "    \n", "    .main-header {\n", "        background: linear-gradient(135deg, #1a1a1a, #2d2d2d);\n", "        color: #ffffff;\n", "        text-align: center;\n", "        padding: 25px;\n", "        border-radius: 10px;\n", "        margin-bottom: 20px;\n", "        border: 1px solid #333;\n", "    }\n", "    \n", "    .chatbot {\n", "        background: #1a1a1a !important;\n", "        border: 1px solid #333 !important;\n", "        border-radius: 10px !important;\n", "    }\n", "    \n", "    .input-box {\n", "        background: #1a1a1a !important;\n", "        border: 2px solid #333 !important;\n", "        border-radius: 20px !important;\n", "        color: #ffffff !important;\n", "        padding: 10px 15px !important;\n", "    }\n", "    \n", "    .send-button {\n", "        background: linear-gradient(45deg, #10a37f, #1a73e8) !important;\n", "        border: none !important;\n", "        border-radius: 50% !important;\n", "        color: white !important;\n", "        width: 40px !important;\n", "        height: 40px !important;\n", "    }\n", "    \n", "    .sidebar {\n", "        background: #1a1a1a !important;\n", "        border: 1px solid #333 !important;\n", "        border-radius: 10px !important;\n", "        padding: 15px !important;\n", "        color: #ffffff !important;\n", "    }\n", "    \n", "    .control-btn {\n", "        background: #2d2d2d !important;\n", "        border: 1px solid #555 !important;\n", "        border-radius: 6px !important;\n", "        color: #ffffff !important;\n", "        margin: 3px 0 !important;\n", "    }\n", "    \"\"\"\n", "    \n", "    # 创建界面\n", "    with gr.Blocks(css=css, title=\"AI游戏主持人 - 流式版本\") as demo:\n", "        # 标题\n", "        gr.HTM<PERSON>(\"\"\"\n", "        <div class=\"main-header\">\n", "            <h1>🎮 AI游戏主持人 - 流式版本</h1>\n", "            <p>Elder Scrolls冒险 | 支持实时流式输出</p>\n", "        </div>\n", "        \"\"\")\n", "        \n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>(scale=3):\n", "                # 聊天界面\n", "                chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                    [],\n", "                    height=500,\n", "                    elem_classes=[\"chatbot\"],\n", "                    show_copy_button=True\n", "                )\n", "                \n", "                with gr.<PERSON>():\n", "                    msg = gr.Textbox(\n", "                        placeholder=\"描述您的行动...\",\n", "                        container=False,\n", "                        scale=6,\n", "                        elem_classes=[\"input-box\"]\n", "                    )\n", "                    submit = gr.<PERSON><PERSON>(\n", "                        \"🚀\",\n", "                        scale=1,\n", "                        elem_classes=[\"send-button\"]\n", "                    )\n", "            \n", "            with gr.<PERSON>(scale=1):\n", "                with gr.Group(elem_classes=[\"sidebar\"]):\n", "                    gr.HTM<PERSON>('<h3>🎛️ 控制面板</h3>')\n", "                    \n", "                    # 状态显示\n", "                    status_display = gr.Textbox(\n", "                        label=\"📊 游戏状态\",\n", "                        lines=8,\n", "                        interactive=False\n", "                    )\n", "                    \n", "                    # 控制按钮\n", "                    refresh_btn = gr.<PERSON><PERSON>(\"🔄 刷新\", elem_classes=[\"control-btn\"])\n", "                    save_btn = gr.<PERSON><PERSON>(\"💾 保存\", elem_classes=[\"control-btn\"])\n", "                    clear_btn = gr.<PERSON><PERSON>(\"🗑️ 清空\", elem_classes=[\"control-btn\"])\n", "                    \n", "                    # 结果显示\n", "                    info_display = gr.Textbox(\n", "                        label=\"ℹ️ 操作结果\",\n", "                        lines=3,\n", "                        interactive=False\n", "                    )\n", "        \n", "        # 事件绑定\n", "        msg.submit(respond_stream, [msg, chatbot], [chatbot, msg])\n", "        submit.click(respond_stream, [msg, chatbot], [chatbot, msg])\n", "        \n", "        # 功能函数\n", "        def update_status():\n", "            session_summary = session_manager.get_summary()\n", "            game_summary = game_state.get_summary()\n", "            context = game_state.get_context()\n", "            \n", "            status = f\"\"\"\n", "📊 会话统计:\n", "• 总消息: {session_summary['total']}\n", "• 玩家: {session_summary['user']}\n", "• AI: {session_summary['assistant']}\n", "\n", "🎮 游戏状态:\n", "• 物品数: {game_summary['items']}\n", "• 金币: {game_summary['gold']}\n", "• 事件数: {game_summary['events']}\n", "\n", "🔍 当前状态:\n", "{context}\n", "            \"\"\".strip()\n", "            return status\n", "        \n", "        def save_game():\n", "            try:\n", "                filepath = session_manager.save_session()\n", "                return f\"✅ 已保存\\n📁 {filepath}\"\n", "            except Exception as e:\n", "                return f\"❌ 保存失败: {e}\"\n", "        \n", "        def clear_chat():\n", "            global session_manager, game_state\n", "            session_manager = SimpleSessionManager()\n", "            game_state = SimpleGameState()\n", "            return \"✅ 已清空\", []\n", "        \n", "        # 绑定功能按钮\n", "        refresh_btn.click(update_status, outputs=status_display)\n", "        save_btn.click(save_game, outputs=info_display)\n", "        clear_btn.click(clear_chat, outputs=[info_display, chatbot])\n", "        \n", "        # 初始化\n", "        demo.load(update_status, outputs=status_display)\n", "    \n", "    return demo\n", "\n", "# 启动界面\n", "if model_manager.model:\n", "    print(\"🚀 启动流式聊天界面...\")\n", "    \n", "    try:\n", "        demo = create_streaming_interface()\n", "        demo.launch(\n", "            share=True,\n", "            debug=False,\n", "            show_error=True,\n", "            inbrowser=True\n", "        )\n", "    except Exception as e:\n", "        print(f\"❌ 界面启动失败: {str(e)}\")\n", "        print(\"\\n🔄 启动简化版界面...\")\n", "        \n", "        # 简化版命令行界面\n", "        def simple_chat():\n", "            print(\"\\n\" + \"=\"*50)\n", "            print(\"🎮 Elder Scrolls AI冒险 - 命令行版\")\n", "            print(\"=\"*50)\n", "            print(\"输入 'quit' 退出 | 'save' 保存 | 'status' 状态\")\n", "            print(\"-\"*50)\n", "            \n", "            while True:\n", "                try:\n", "                    user_input = input(\"\\n🎮 您的行动: \").strip()\n", "                    \n", "                    if user_input.lower() in ['quit', 'exit']:\n", "                        print(\"👋 再见！\")\n", "                        break\n", "                    elif user_input.lower() == 'save':\n", "                        filepath = session_manager.save_session()\n", "                        print(f\"💾 已保存: {filepath}\")\n", "                        continue\n", "                    elif user_input.lower() == 'status':\n", "                        summary = session_manager.get_summary()\n", "                        game_summary = game_state.get_summary()\n", "                        print(f\"\\n📊 状态: 消息{summary['total']} | 物品{game_summary['items']} | 金币{game_summary['gold']}\")\n", "                        continue\n", "                    elif not user_input:\n", "                        continue\n", "                    \n", "                    # 处理输入\n", "                    session_manager.add_message(\"user\", user_input)\n", "                    game_state.analyze_message(\"user\", user_input)\n", "                    \n", "                    messages = session_manager.get_messages_for_model()\n", "                    context = game_state.get_context()\n", "                    \n", "                    if context:\n", "                        enhanced_content = f\"{user_input}\\n\\n游戏状态:\\n{context}\"\n", "                        messages[-1][\"content\"] = enhanced_content\n", "                    \n", "                    print(\"\\n🎭 游戏主持人: \", end=\"\", flush=True)\n", "                    \n", "                    # 流式输出到控制台\n", "                    full_response = \"\"\n", "                    for partial in model_manager.generate_response_stream(messages):\n", "                        # 只打印新增的部分\n", "                        new_part = partial[len(full_response):]\n", "                        print(new_part, end=\"\", flush=True)\n", "                        full_response = partial\n", "                    \n", "                    print()  # 换行\n", "                    \n", "                    session_manager.add_message(\"assistant\", full_response)\n", "                    game_state.analyze_message(\"assistant\", full_response)\n", "                    \n", "                except KeyboardInterrupt:\n", "                    print(\"\\n👋 游戏中断！\")\n", "                    break\n", "                except Exception as e:\n", "                    print(f\"\\n❌ 错误: {e}\")\n", "        \n", "        simple_chat()\n", "else:\n", "    print(\"❌ 请先加载模型！\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}