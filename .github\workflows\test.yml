name: Test AI Game Master

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov flake8
    
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Test configuration loading
      run: |
        python -c "from config import Config, validate_config; validate_config(); print('✓ Configuration validation passed')"
    
    - name: Test model manager (CPU only)
      run: |
        python -c "
        from model_manager import ModelManager
        manager = ModelManager()
        print('✓ Model manager initialization passed')
        print(f'GPU available: {manager.device}')
        memory_info = manager.check_gpu_memory()
        print(f'Memory info: {memory_info}')
        "
    
    - name: Test imports
      run: |
        python -c "
        try:
            import config
            import model_manager
            print('✓ All modules import successfully')
        except ImportError as e:
            print(f'✗ Import error: {e}')
            exit(1)
        "

  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install linting tools
      run: |
        python -m pip install --upgrade pip
        pip install black isort flake8 mypy
    
    - name: Check code formatting with black
      run: |
        black --check --diff .
    
    - name: Check import sorting with isort
      run: |
        isort --check-only --diff .
    
    - name: Lint with flake8
      run: |
        flake8 . --max-line-length=127 --extend-ignore=E203,W503
