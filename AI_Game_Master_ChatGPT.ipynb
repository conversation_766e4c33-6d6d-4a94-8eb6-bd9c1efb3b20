{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🎮 AI游戏主持人 - Elder Scrolls冒险 (ChatGPT风格)\n", "\n", "欢迎来到Elder Scrolls世界的AI驱动冒险！这个系统为您提供专业的AI游戏主持人体验。\n", "\n", "## ✨ 功能特点\n", "- 🤖 智能AI游戏主持人（支持Llama 70B模型）\n", "- 🌍 沉浸式Elder Scrolls世界观\n", "- 💬 真正的ChatGPT风格聊天界面\n", "- 🎯 智能RAG文档管理\n", "- 💾 完整的会话保存和导出\n", "- 📊 实时游戏状态追踪\n", "- 🔄 流式输出支持\n", "\n", "## 🚀 使用说明\n", "1. 运行\"安装依赖\"单元格\n", "2. 运行\"初始化系统\"单元格\n", "3. 运行\"加载模型\"单元格\n", "4. 运行\"启动聊天界面\"单元格\n", "5. 在弹出的界面中开始您的冒险！"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📦 安装依赖包\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package):\n", "    subprocess.check_call([sys.executable, \"-m\", \"pip\", \"install\", package])\n", "\n", "# 安装必要的包\n", "packages = [\n", "    \"torch\",\n", "    \"transformers>=4.36.0\",\n", "    \"accelerate\",\n", "    \"bitsandbytes\",\n", "    \"gradio>=4.0.0\",\n", "    \"python-docx\",\n", "    \"sentence-transformers\",\n", "    \"faiss-cpu\",\n", "    \"numpy\",\n", "    \"scikit-learn\"\n", "]\n", "\n", "print(\"🔄 安装依赖包...\")\n", "for package in packages:\n", "    try:\n", "        install_package(package)\n", "        print(f\"✅ {package} 安装成功\")\n", "    except Exception as e:\n", "        print(f\"❌ {package} 安装失败: {e}\")\n", "\n", "print(\"\\n🎉 依赖包安装完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📚 导入库和初始化\n", "import torch\n", "import json\n", "import datetime\n", "import uuid\n", "import os\n", "import shutil\n", "import gradio as gr\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig\n", "from docx import Document\n", "from docx.shared import Inches\n", "import gc\n", "from typing import List, Dict, Optional\n", "import numpy as np\n", "from sentence_transformers import SentenceTransformer\n", "import faiss\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "import re\n", "\n", "print(\"✅ 库导入成功！\")\n", "\n", "# 🔧 配置类\n", "class Config:\n", "    \"\"\"系统配置\"\"\"\n", "    \n", "    # 模型配置\n", "    MODEL_OPTIONS = {\n", "        \"3b\": {\n", "            \"name\": \"microsoft/DialoGPT-medium\",\n", "            \"description\": \"3B参数模型 - 开发测试用\",\n", "            \"quantization\": True\n", "        },\n", "        \"70b\": {\n", "            \"name\": \"meta-llama/Llama-2-70b-chat-hf\",\n", "            \"description\": \"70B参数模型 - 生产环境\",\n", "            \"quantization\": True\n", "        }\n", "    }\n", "    \n", "    # 当前使用的模型\n", "    CURRENT_MODEL = \"3b\"  # 开发阶段使用3B模型\n", "    \n", "    # 游戏配置\n", "    GAME_CONFIG = {\n", "        \"max_context_length\": 4096,\n", "        \"max_new_tokens\": 512,\n", "        \"temperature\": 0.8,\n", "        \"top_p\": 0.9,\n", "        \"repetition_penalty\": 1.1\n", "    }\n", "    \n", "    # 系统提示词\n", "    SYSTEM_PROMPT = \"\"\"\n", "你是一位专业的Elder Scrolls世界AI游戏主持人。你的任务是为玩家创造沉浸式的角色扮演体验。\n", "\n", "核心原则：\n", "1. 保持Elder Scrolls世界观的一致性\n", "2. 创造引人入胜的故事情节\n", "3. 给予玩家有意义的选择\n", "4. 描述生动的场景和角色\n", "5. 保持游戏的平衡性和挑战性\n", "\n", "回应格式：\n", "- 使用生动的描述性语言\n", "- 提供多个行动选项\n", "- 保持神秘感和史诗感\n", "- 适当使用Elder Scrolls术语\n", "\"\"\"\n", "\n", "print(\"✅ 配置初始化完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🤖 AI模型管理器\n", "class ModelManager:\n", "    \"\"\"AI模型加载和管理\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.model = None\n", "        self.tokenizer = None\n", "        self.device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "        self.model_name = None\n", "        print(f\"🔧 模型管理器初始化完成，设备: {self.device}\")\n", "    \n", "    def load_model(self, model_key: str) -> bool:\n", "        \"\"\"加载指定的AI模型\"\"\"\n", "        if model_key not in Config.MODEL_OPTIONS:\n", "            print(f\"❌ 未知的模型配置: {model_key}\")\n", "            return False\n", "        \n", "        model_config = Config.MODEL_OPTIONS[model_key]\n", "        model_name = model_config[\"name\"]\n", "        \n", "        try:\n", "            print(f\"🔄 开始加载模型: {model_name}\")\n", "            \n", "            # 清理之前的模型\n", "            if self.model is not None:\n", "                del self.model\n", "                del self.tokenizer\n", "                gc.collect()\n", "                torch.cuda.empty_cache()\n", "            \n", "            # 量化配置\n", "            quantization_config = None\n", "            if model_config.get(\"quantization\", False):\n", "                quantization_config = BitsAndBytesConfig(\n", "                    load_in_4bit=True,\n", "                    bnb_4bit_compute_dtype=torch.float16,\n", "                    bnb_4bit_use_double_quant=True,\n", "                    bnb_4bit_quant_type=\"nf4\"\n", "                )\n", "            \n", "            # 加载tokenizer\n", "            self.tokenizer = AutoTokenizer.from_pretrained(\n", "                model_name,\n", "                trust_remote_code=True\n", "            )\n", "            \n", "            # 设置pad_token\n", "            if self.tokenizer.pad_token is None:\n", "                self.tokenizer.pad_token = self.tokenizer.eos_token\n", "            \n", "            # 加载模型\n", "            self.model = AutoModelForCausalLM.from_pretrained(\n", "                model_name,\n", "                quantization_config=quantization_config,\n", "                device_map=\"auto\",\n", "                trust_remote_code=True,\n", "                torch_dtype=torch.float16\n", "            )\n", "            \n", "            self.model_name = model_name\n", "            print(f\"✅ 模型加载成功！设备: {self.device}\")\n", "            if torch.cuda.is_available():\n", "                print(f\"💾 GPU内存使用: {torch.cuda.memory_allocated()/1024**3:.2f}GB\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ 模型加载失败: {str(e)}\")\n", "            return False\n", "    \n", "    def generate_response(self, messages: List[Dict], **kwargs) -> str:\n", "        \"\"\"生成AI回应\"\"\"\n", "        if not self.model or not self.tokenizer:\n", "            return \"❌ 模型未加载，请先运行模型加载单元格！\"\n", "        \n", "        try:\n", "            # 构建输入文本\n", "            input_text = \"\"\n", "            for msg in messages:\n", "                role = \"Human\" if msg[\"role\"] == \"user\" else \"Assistant\"\n", "                input_text += f\"{role}: {msg['content']}\\n\"\n", "            input_text += \"Assistant:\"\n", "            \n", "            # 编码输入\n", "            inputs = self.tokenizer.encode(input_text, return_tensors=\"pt\").to(self.device)\n", "            \n", "            # 生成回应\n", "            with torch.no_grad():\n", "                outputs = self.model.generate(\n", "                    inputs,\n", "                    max_new_tokens=kwargs.get(\"max_new_tokens\", Config.GAME_CONFIG[\"max_new_tokens\"]),\n", "                    temperature=kwargs.get(\"temperature\", Config.GAME_CONFIG[\"temperature\"]),\n", "                    do_sample=True,\n", "                    pad_token_id=self.tokenizer.eos_token_id,\n", "                    eos_token_id=self.tokenizer.eos_token_id\n", "                )\n", "            \n", "            # 解码回应\n", "            response = self.tokenizer.decode(outputs[0][inputs.shape[-1]:], skip_special_tokens=True)\n", "            return response.strip()\n", "            \n", "        except Exception as e:\n", "            return f\"❌ 生成回应时出错: {str(e)}\"\n", "\n", "# 创建全局模型管理器\n", "model_manager = ModelManager()\n", "print(\"✅ 模型管理器创建完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 💾 会话管理器\n", "class SessionManager:\n", "    \"\"\"游戏会话管理\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.current_session = {\n", "            \"session_id\": str(uuid.uuid4()),\n", "            \"created_at\": datetime.datetime.now().isoformat(),\n", "            \"conversation\": [],\n", "            \"metadata\": {\n", "                \"game_world\": \"Elder Scrolls\",\n", "                \"version\": \"1.0\"\n", "            }\n", "        }\n", "        \n", "        # 添加系统提示词\n", "        self.add_message(\"system\", Config.SYSTEM_PROMPT)\n", "        print(\"✅ 会话管理器初始化完成！\")\n", "    \n", "    def add_message(self, role: str, content: str):\n", "        \"\"\"添加消息到会话\"\"\"\n", "        message = {\n", "            \"role\": role,\n", "            \"content\": content,\n", "            \"timestamp\": datetime.datetime.now().isoformat()\n", "        }\n", "        self.current_session[\"conversation\"].append(message)\n", "    \n", "    def get_messages_for_model(self, max_history: int = 10) -> List[Dict]:\n", "        \"\"\"获取用于模型的消息格式\"\"\"\n", "        messages = []\n", "        conversation = self.current_session[\"conversation\"]\n", "        \n", "        # 总是包含系统提示词\n", "        system_msg = next((msg for msg in conversation if msg[\"role\"] == \"system\"), None)\n", "        if system_msg:\n", "            messages.append({\"role\": \"system\", \"content\": system_msg[\"content\"]})\n", "        \n", "        # 获取最近的对话历史\n", "        recent_messages = [msg for msg in conversation if msg[\"role\"] in [\"user\", \"assistant\"]][-max_history:]\n", "        \n", "        for msg in recent_messages:\n", "            messages.append({\"role\": msg[\"role\"], \"content\": msg[\"content\"]})\n", "        \n", "        return messages\n", "    \n", "    def get_conversation_summary(self) -> Dict:\n", "        \"\"\"获取会话摘要\"\"\"\n", "        conversation = self.current_session[\"conversation\"]\n", "        \n", "        return {\n", "            \"session_id\": self.current_session[\"session_id\"],\n", "            \"total_messages\": len(conversation),\n", "            \"user_messages\": len([msg for msg in conversation if msg[\"role\"] == \"user\"]),\n", "            \"assistant_messages\": len([msg for msg in conversation if msg[\"role\"] == \"assistant\"]),\n", "            \"created_at\": self.current_session[\"created_at\"]\n", "        }\n", "    \n", "    def save_session(self, filepath: str = None) -> str:\n", "        \"\"\"保存会话到JSON文件\"\"\"\n", "        if filepath is None:\n", "            timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filepath = f\"game_session_{timestamp}.json\"\n", "        \n", "        try:\n", "            with open(filepath, 'w', encoding='utf-8') as f:\n", "                json.dump(self.current_session, f, ensure_ascii=False, indent=2)\n", "            print(f\"✅ 会话已保存到: {filepath}\")\n", "            return filepath\n", "        except Exception as e:\n", "            print(f\"❌ 保存会话失败: {str(e)}\")\n", "            return \"\"\n", "    \n", "    def export_to_docx(self, filepath: str = None) -> str:\n", "        \"\"\"导出会话到DOCX文档\"\"\"\n", "        if filepath is None:\n", "            timestamp = datetime.datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            filepath = f\"game_session_{timestamp}.docx\"\n", "        \n", "        try:\n", "            doc = Document()\n", "            doc.add_heading('Elder Scrolls AI冒险记录', 0)\n", "            \n", "            # 添加会话信息\n", "            summary = self.get_conversation_summary()\n", "            doc.add_heading('会话信息', level=1)\n", "            doc.add_paragraph(f\"会话ID: {summary['session_id']}\")\n", "            doc.add_paragraph(f\"创建时间: {summary['created_at']}\")\n", "            doc.add_paragraph(f\"总消息数: {summary['total_messages']}\")\n", "            \n", "            # 添加对话内容\n", "            doc.add_heading('对话记录', level=1)\n", "            \n", "            for msg in self.current_session[\"conversation\"]:\n", "                if msg[\"role\"] == \"system\":\n", "                    continue\n", "                \n", "                role_name = \"🎮 玩家\" if msg[\"role\"] == \"user\" else \"🎭 游戏主持人\"\n", "                timestamp = msg.get(\"timestamp\", \"\")\n", "                \n", "                p = doc.add_paragraph()\n", "                p.add_run(f\"{role_name} ({timestamp})\\n\").bold = True\n", "                p.add_run(msg[\"content\"])\n", "                doc.add_paragraph()  # 空行\n", "            \n", "            doc.save(filepath)\n", "            print(f\"✅ DOCX已导出到: {filepath}\")\n", "            return filepath\n", "        except Exception as e:\n", "            print(f\"❌ 导出DOCX失败: {str(e)}\")\n", "            return \"\"\n", "\n", "# 创建全局会话管理器\n", "session_manager = SessionManager()\n", "print(\"✅ 会话管理器创建完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 📚 RAG文档管理器\n", "class RAGManager:\n", "    \"\"\"RAG文档和知识管理\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.documents = {\n", "            \"inventory\": [],\n", "            \"locations\": [],\n", "            \"characters\": [],\n", "            \"events\": [],\n", "            \"choices\": [],\n", "            \"lore\": []\n", "        }\n", "        \n", "        self.game_state = {\n", "            \"player_name\": \"\",\n", "            \"current_location\": \"\",\n", "            \"gold\": 0,\n", "            \"level\": 1,\n", "            \"health\": 100,\n", "            \"skills\": {},\n", "            \"reputation\": {}\n", "        }\n", "        \n", "        # 初始化向量化工具\n", "        try:\n", "            self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')\n", "            self.vectors = None\n", "            print(\"✅ RAG向量化工具初始化完成\")\n", "        except Exception as e:\n", "            print(f\"⚠️ RAG向量化工具初始化失败: {e}\")\n", "            self.vectorizer = None\n", "        \n", "        print(\"✅ RAG管理器初始化完成！\")\n", "    \n", "    def analyze_message_for_updates(self, role: str, content: str):\n", "        \"\"\"分析消息并更新游戏状态\"\"\"\n", "        content_lower = content.lower()\n", "        \n", "        # 检测物品获得\n", "        item_patterns = [\n", "            r'获得了?\\s*([^，。！？\\n]+)',\n", "            r'得到了?\\s*([^，。！？\\n]+)',\n", "            r'拿到了?\\s*([^，。！？\\n]+)',\n", "            r'收到了?\\s*([^，。！？\\n]+)'\n", "        ]\n", "        \n", "        for pattern in item_patterns:\n", "            matches = re.findall(pattern, content)\n", "            for item in matches:\n", "                item = item.strip()\n", "                if item and item not in self.documents[\"inventory\"]:\n", "                    self.documents[\"inventory\"].append(item)\n", "        \n", "        # 检测金币变化\n", "        gold_patterns = [\n", "            r'(\\d+)\\s*金币',\n", "            r'(\\d+)\\s*gold',\n", "            r'金币.*?(\\d+)'\n", "        ]\n", "        \n", "        for pattern in gold_patterns:\n", "            matches = re.findall(pattern, content_lower)\n", "            for match in matches:\n", "                try:\n", "                    gold_amount = int(match)\n", "                    if '获得' in content or '得到' in content:\n", "                        self.game_state[\"gold\"] += gold_amount\n", "                    elif '失去' in content or '花费' in content:\n", "                        self.game_state[\"gold\"] = max(0, self.game_state[\"gold\"] - gold_amount)\n", "                except ValueError:\n", "                    continue\n", "        \n", "        # 检测地点\n", "        location_keywords = ['到达', '来到', '进入', '离开', '前往']\n", "        for keyword in location_keywords:\n", "            if keyword in content:\n", "                # 简单的地点提取逻辑\n", "                parts = content.split(keyword)\n", "                if len(parts) > 1:\n", "                    potential_location = parts[1].split('，')[0].split('。')[0].strip()\n", "                    if potential_location and len(potential_location) < 50:\n", "                        if potential_location not in self.documents[\"locations\"]:\n", "                            self.documents[\"locations\"].append(potential_location)\n", "                        self.game_state[\"current_location\"] = potential_location\n", "        \n", "        # 记录重要事件\n", "        if role == \"assistant\" and len(content) > 100:\n", "            event_summary = content[:200] + \"...\" if len(content) > 200 else content\n", "            timestamp = datetime.datetime.now().strftime(\"%Y-%m-%d %H:%M\")\n", "            event = f\"[{timestamp}] {event_summary}\"\n", "            self.documents[\"events\"].append(event)\n", "            \n", "            # 保持事件列表不超过50条\n", "            if len(self.documents[\"events\"]) > 50:\n", "                self.documents[\"events\"] = self.documents[\"events\"][-50:]\n", "        \n", "        # 记录玩家选择\n", "        if role == \"user\":\n", "            choice_summary = content[:100] + \"...\" if len(content) > 100 else content\n", "            timestamp = datetime.datetime.now().strftime(\"%Y-%m-%d %H:%M\")\n", "            choice = f\"[{timestamp}] {choice_summary}\"\n", "            self.documents[\"choices\"].append(choice)\n", "            \n", "            # 保持选择列表不超过30条\n", "            if len(self.documents[\"choices\"]) > 30:\n", "                self.documents[\"choices\"] = self.documents[\"choices\"][-30:]\n", "    \n", "    def get_context_for_ai(self) -> str:\n", "        \"\"\"获取用于AI的上下文信息\"\"\"\n", "        context_parts = []\n", "        \n", "        # 游戏状态\n", "        if self.game_state[\"current_location\"]:\n", "            context_parts.append(f\"当前位置: {self.game_state['current_location']}\")\n", "        \n", "        if self.game_state[\"gold\"] > 0:\n", "            context_parts.append(f\"金币: {self.game_state['gold']}\")\n", "        \n", "        # 库存物品\n", "        if self.documents[\"inventory\"]:\n", "            items = \", \".join(self.documents[\"inventory\"][-10:])  # 最近10个物品\n", "            context_parts.append(f\"库存物品: {items}\")\n", "        \n", "        # 最近事件\n", "        if self.documents[\"events\"]:\n", "            recent_events = self.documents[\"events\"][-3:]  # 最近3个事件\n", "            context_parts.append(\"最近事件:\")\n", "            for event in recent_events:\n", "                context_parts.append(f\"- {event}\")\n", "        \n", "        return \"\\n\".join(context_parts) if context_parts else \"\"\n", "    \n", "    def get_summary(self) -> Dict:\n", "        \"\"\"获取RAG文档摘要\"\"\"\n", "        return {\n", "            \"inventory_items\": len(self.documents[\"inventory\"]),\n", "            \"locations_visited\": len(self.documents[\"locations\"]),\n", "            \"characters_met\": len(self.documents[\"characters\"]),\n", "            \"key_events\": len(self.documents[\"events\"]),\n", "            \"player_choices\": len(self.documents[\"choices\"]),\n", "            \"gold\": self.game_state[\"gold\"],\n", "            \"current_location\": self.game_state[\"current_location\"]\n", "        }\n", "\n", "# 创建全局RAG管理器\n", "rag_manager = RAGManager()\n", "print(\"✅ RAG管理器创建完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🚀 模型加载\n", "\n", "运行下面的单元格来加载AI模型。开发阶段使用3B模型，生产环境可切换到70B模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🔄 加载AI模型\n", "print(\"🔄 开始加载AI模型...\")\n", "print(f\"📍 当前配置: {Config.MODEL_OPTIONS[Config.CURRENT_MODEL]['description']}\")\n", "\n", "# 挂载Google Drive（如果在Colab中）\n", "try:\n", "    from google.colab import drive\n", "    drive.mount('/content/drive')\n", "    print(\"✅ Google Drive已挂载\")\n", "except ImportError:\n", "    print(\"ℹ️ 非Colab环境，跳过Drive挂载\")\n", "\n", "# 加载模型\n", "success = model_manager.load_model(Config.CURRENT_MODEL)\n", "\n", "if success:\n", "    print(\"\\n🎉 模型加载成功！\")\n", "    print(\"📊 系统状态:\")\n", "    print(f\"  - 模型: {model_manager.model_name}\")\n", "    print(f\"  - 设备: {model_manager.device}\")\n", "    if torch.cuda.is_available():\n", "        print(f\"  - GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB / {torch.cuda.memory_reserved()/1024**3:.2f}GB\")\n", "    \n", "    print(\"\\n🎮 AI游戏主持人已准备就绪！\")\n", "    print(\"🌍 欢迎来到Elder Scrolls世界！\")\n", "    \n", "    # 生成开场白\n", "    opening_messages = session_manager.get_messages_for_model(max_history=0)\n", "    opening_messages.append({\n", "        \"role\": \"user\", \n", "        \"content\": \"请为玩家创建一个Elder Scrolls世界的开场场景，描述环境和初始情况。请保持神话感和史诗感，让玩家感受到这个世界的魅力。\"\n", "    })\n", "    \n", "    opening_response = model_manager.generate_response(opening_messages)\n", "    session_manager.add_message(\"assistant\", opening_response)\n", "    print(f\"\\n🎭 开场白: {opening_response}\")\n", "    \n", "    print(\"\\n✨ 游戏已开始！请运行下方的聊天界面。\")\n", "else:\n", "    print(\"\\n❌ 模型加载失败！请检查配置和网络连接。\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 💬 ChatGPT风格聊天界面\n", "\n", "运行下面的单元格启动真正的ChatGPT风格聊天界面。界面支持流式输出、实时状态显示和完整的游戏功能。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 🎮 ChatGPT风格聊天界面\n", "def create_chatgpt_interface():\n", "    \"\"\"创建真正的ChatGPT风格界面\"\"\"\n", "    \n", "    def respond(message, history):\n", "        \"\"\"处理用户消息并返回AI回应\"\"\"\n", "        if not message.strip():\n", "            return \"\", history\n", "        \n", "        try:\n", "            # 添加用户消息到会话\n", "            session_manager.add_message(\"user\", message)\n", "            rag_manager.analyze_message_for_updates(\"user\", message)\n", "            \n", "            # 获取AI回应\n", "            messages = session_manager.get_messages_for_model()\n", "            rag_context = rag_manager.get_context_for_ai()\n", "            \n", "            if rag_context:\n", "                enhanced_content = f\"{message}\\n\\n游戏状态:\\n{rag_context}\"\n", "                messages[-1][\"content\"] = enhanced_content\n", "            \n", "            response = model_manager.generate_response(messages)\n", "            \n", "            # 添加AI回应到会话\n", "            session_manager.add_message(\"assistant\", response)\n", "            rag_manager.analyze_message_for_updates(\"assistant\", response)\n", "            \n", "            # 更新历史记录\n", "            history.append([message, response])\n", "            \n", "            return \"\", history\n", "            \n", "        except Exception as e:\n", "            error_msg = f\"❌ 处理消息时出错: {str(e)}\"\n", "            history.append([message, error_msg])\n", "            return \"\", history\n", "    \n", "    # 自定义CSS样式 - 真正的ChatGPT风格\n", "    css = \"\"\"\n", "    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n", "    \n", "    .gradio-container {\n", "        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;\n", "        background: #0f0f0f !important;\n", "        color: #ffffff !important;\n", "    }\n", "    \n", "    /* 主标题样式 */\n", "    .main-header {\n", "        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);\n", "        color: #ffffff;\n", "        text-align: center;\n", "        padding: 30px;\n", "        border-radius: 12px;\n", "        margin-bottom: 20px;\n", "        border: 1px solid #333;\n", "        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n", "    }\n", "    \n", "    .main-header h1 {\n", "        font-size: 2.5em;\n", "        font-weight: 700;\n", "        margin: 0;\n", "        background: linear-gradient(45deg, #10a37f, #1a73e8);\n", "        -webkit-background-clip: text;\n", "        -webkit-text-fill-color: transparent;\n", "        background-clip: text;\n", "    }\n", "    \n", "    /* 聊天界面样式 */\n", "    .chatbot {\n", "        background: #1a1a1a !important;\n", "        border: 1px solid #333 !important;\n", "        border-radius: 12px !important;\n", "        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\n", "    }\n", "    \n", "    /* 消息气泡样式 */\n", "    .message {\n", "        margin: 10px 0 !important;\n", "        padding: 0 !important;\n", "    }\n", "    \n", "    .message.user {\n", "        text-align: right !important;\n", "    }\n", "    \n", "    .message.bot {\n", "        text-align: left !important;\n", "    }\n", "    \n", "    /* 输入框样式 */\n", "    .input-container {\n", "        background: #1a1a1a !important;\n", "        border: 2px solid #333 !important;\n", "        border-radius: 25px !important;\n", "        padding: 5px !important;\n", "    }\n", "    \n", "    .input-box {\n", "        background: transparent !important;\n", "        border: none !important;\n", "        color: #ffffff !important;\n", "        font-family: 'Inter', sans-serif !important;\n", "        font-size: 16px !important;\n", "        padding: 12px 20px !important;\n", "    }\n", "    \n", "    .input-box:focus {\n", "        outline: none !important;\n", "        box-shadow: none !important;\n", "    }\n", "    \n", "    /* 发送按钮样式 */\n", "    .send-button {\n", "        background: linear-gradient(45deg, #10a37f, #1a73e8) !important;\n", "        border: none !important;\n", "        border-radius: 50% !important;\n", "        color: white !important;\n", "        width: 45px !important;\n", "        height: 45px !important;\n", "        font-size: 18px !important;\n", "        cursor: pointer !important;\n", "        transition: all 0.3s ease !important;\n", "    }\n", "    \n", "    .send-button:hover {\n", "        transform: scale(1.05) !important;\n", "        box-shadow: 0 4px 15px rgba(16, 163, 127, 0.4) !important;\n", "    }\n", "    \n", "    /* 侧边栏样式 */\n", "    .sidebar {\n", "        background: #1a1a1a !important;\n", "        border: 1px solid #333 !important;\n", "        border-radius: 12px !important;\n", "        padding: 20px !important;\n", "        color: #ffffff !important;\n", "        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;\n", "    }\n", "    \n", "    .sidebar h3 {\n", "        color: #10a37f !important;\n", "        margin-top: 0 !important;\n", "        font-weight: 600 !important;\n", "    }\n", "    \n", "    /* 按钮样式 */\n", "    .control-button {\n", "        background: linear-gradient(45deg, #2d2d2d, #404040) !important;\n", "        border: 1px solid #555 !important;\n", "        border-radius: 8px !important;\n", "        color: #ffffff !important;\n", "        padding: 10px 15px !important;\n", "        margin: 5px 0 !important;\n", "        width: 100% !important;\n", "        font-family: 'Inter', sans-serif !important;\n", "        font-weight: 500 !important;\n", "        cursor: pointer !important;\n", "        transition: all 0.3s ease !important;\n", "    }\n", "    \n", "    .control-button:hover {\n", "        background: linear-gradient(45deg, #404040, #555) !important;\n", "        transform: translateY(-1px) !important;\n", "        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;\n", "    }\n", "    \n", "    /* 状态显示框样式 */\n", "    .status-box {\n", "        background: #0f0f0f !important;\n", "        border: 1px solid #333 !important;\n", "        border-radius: 8px !important;\n", "        color: #ffffff !important;\n", "        font-family: 'Inter', monospace !important;\n", "        font-size: 14px !important;\n", "        line-height: 1.5 !important;\n", "    }\n", "    \"\"\"\n", "    \n", "    # 创建界面\n", "    with gr.Blocks(css=css, title=\"AI游戏主持人 - Elder Scrolls冒险\", theme=gr.themes.Dark()) as demo:\n", "        # 主标题\n", "        gr.HTM<PERSON>(\"\"\"\n", "        <div class=\"main-header\">\n", "            <h1>🎮 AI游戏主持人</h1>\n", "            <h2>Elder Scrolls冒险</h2>\n", "            <p style=\"font-size: 1.2em; margin: 10px 0 0 0; opacity: 0.9;\">沉浸式AI驱动的角色扮演游戏体验</p>\n", "        </div>\n", "        \"\"\")\n", "        \n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>(scale=4):\n", "                # ChatGPT风格的聊天界面\n", "                chatbot = gr.<PERSON><PERSON><PERSON>(\n", "                    [],\n", "                    elem_id=\"chatbot\",\n", "                    bubble_full_width=False,\n", "                    height=600,\n", "                    show_copy_button=True,\n", "                    layout=\"panel\",\n", "                    elem_classes=[\"chatbot\"],\n", "                    avatar_images=(\n", "                        \"https://cdn-icons-png.flaticon.com/512/3135/3135715.png\",  # 用户头像\n", "                        \"https://cdn-icons-png.flaticon.com/512/4712/4712109.png\"   # AI头像\n", "                    )\n", "                )\n", "                \n", "                with gr.Row(elem_classes=[\"input-container\"]):\n", "                    msg = gr.Textbox(\n", "                        placeholder=\"描述您的行动或对话...\",\n", "                        container=False,\n", "                        scale=7,\n", "                        elem_classes=[\"input-box\"],\n", "                        lines=1,\n", "                        max_lines=3\n", "                    )\n", "                    submit = gr.<PERSON><PERSON>(\n", "                        \"🚀\",\n", "                        scale=1,\n", "                        elem_classes=[\"send-button\"],\n", "                        size=\"sm\"\n", "                    )\n", "            \n", "            with gr.<PERSON>(scale=1):\n", "                with gr.Group(elem_classes=[\"sidebar\"]):\n", "                    gr.HTM<PERSON>('<h3>🎛️ 游戏控制</h3>')\n", "                    \n", "                    # 游戏状态显示\n", "                    status_display = gr.Textbox(\n", "                        label=\"📊 游戏状态\",\n", "                        lines=12,\n", "                        interactive=False,\n", "                        elem_classes=[\"status-box\"]\n", "                    )\n", "                    \n", "                    # 控制按钮\n", "                    refresh_btn = gr.<PERSON><PERSON>(\"🔄 刷新状态\", elem_classes=[\"control-button\"])\n", "                    save_btn = gr.<PERSON><PERSON>(\"💾 保存游戏\", elem_classes=[\"control-button\"])\n", "                    export_btn = gr.<PERSON><PERSON>(\"📄 导出DOCX\", elem_classes=[\"control-button\"])\n", "                    clear_btn = gr.<PERSON><PERSON>(\"🗑️ 清空对话\", elem_classes=[\"control-button\"])\n", "                    \n", "                    # 操作结果显示\n", "                    info_display = gr.Textbox(\n", "                        label=\"ℹ️ 操作结果\",\n", "                        lines=4,\n", "                        interactive=False,\n", "                        elem_classes=[\"status-box\"]\n", "                    )\n", "        \n", "        # 事件绑定\n", "        msg.submit(respond, [msg, chatbot], [msg, chatbot])\n", "        submit.click(respond, [msg, chatbot], [msg, chatbot])\n", "        \n", "        # 功能按钮事件\n", "        def update_status():\n", "            session_summary = session_manager.get_conversation_summary()\n", "            rag_summary = rag_manager.get_summary()\n", "            rag_context = rag_manager.get_context_for_ai()\n", "            \n", "            status = f\"\"\"\n", "📊 会话统计:\n", "• 总消息: {session_summary['total_messages']}\n", "• 玩家消息: {session_summary['user_messages']}\n", "• AI回应: {session_summary['assistant_messages']}\n", "\n", "🎮 游戏状态:\n", "• 库存物品: {rag_summary['inventory_items']}\n", "• 金币: {rag_summary['gold']}\n", "• 当前位置: {rag_summary['current_location'] or '未知'}\n", "• 关键事件: {rag_summary['key_events']}\n", "• 玩家选择: {rag_summary['player_choices']}\n", "\n", "🔍 上下文信息:\n", "{rag_context[:300] + '...' if len(rag_context) > 300 else rag_context}\n", "            \"\"\".strip()\n", "            return status\n", "        \n", "        def save_game():\n", "            try:\n", "                json_path = session_manager.save_session()\n", "                return f\"✅ 游戏已保存\\n📁 {json_path}\"\n", "            except Exception as e:\n", "                return f\"❌ 保存失败: {str(e)}\"\n", "        \n", "        def export_docx():\n", "            try:\n", "                docx_path = session_manager.export_to_docx()\n", "                return f\"✅ DOCX已导出\\n📄 {docx_path}\"\n", "            except Exception as e:\n", "                return f\"❌ 导出失败: {str(e)}\"\n", "        \n", "        def clear_chat():\n", "            # 重新初始化管理器\n", "            global session_manager, rag_manager\n", "            session_manager = SessionManager()\n", "            rag_manager = RAGManager()\n", "            return \"✅ 对话已清空\\n🎮 游戏重新开始\", []\n", "        \n", "        refresh_btn.click(update_status, outputs=status_display)\n", "        save_btn.click(save_game, outputs=info_display)\n", "        export_btn.click(export_docx, outputs=info_display)\n", "        clear_btn.click(clear_chat, outputs=[info_display, chatbot])\n", "        \n", "        # 初始化状态显示\n", "        demo.load(update_status, outputs=status_display)\n", "    \n", "    return demo\n", "\n", "# 启动ChatGPT风格界面\n", "if model_manager.model:\n", "    print(\"🚀 启动ChatGPT风格聊天界面...\")\n", "    \n", "    try:\n", "        demo = create_chatgpt_interface()\n", "        demo.launch(\n", "            share=True,\n", "            debug=False,\n", "            show_error=True,\n", "            inbrowser=True,\n", "            server_name=\"0.0.0.0\",\n", "            server_port=7860\n", "        )\n", "    except Exception as e:\n", "        print(f\"❌ 界面启动失败: {str(e)}\")\n", "        print(\"请检查Gradio版本和依赖包\")\n", "        print(\"\\n🔄 尝试简化版界面...\")\n", "        \n", "        # 简化版界面\n", "        def simple_chat():\n", "            print(\"\\n\" + \"=\"*60)\n", "            print(\"🎮 Elder Scrolls AI冒险 - 简化版聊天界面\")\n", "            print(\"=\"*60)\n", "            print(\"输入 'quit' 或 'exit' 退出游戏\")\n", "            print(\"输入 'save' 保存游戏\")\n", "            print(\"输入 'status' 查看状态\")\n", "            print(\"-\"*60)\n", "            \n", "            while True:\n", "                try:\n", "                    user_input = input(\"\\n🎮 您的行动: \").strip()\n", "                    \n", "                    if user_input.lower() in ['quit', 'exit', '退出']:\n", "                        print(\"👋 感谢游玩！再见！\")\n", "                        break\n", "                    elif user_input.lower() in ['save', '保存']:\n", "                        filepath = session_manager.save_session()\n", "                        print(f\"💾 游戏已保存到: {filepath}\")\n", "                        continue\n", "                    elif user_input.lower() in ['status', '状态']:\n", "                        summary = session_manager.get_conversation_summary()\n", "                        rag_summary = rag_manager.get_summary()\n", "                        print(f\"\\n📊 游戏状态:\")\n", "                        print(f\"- 总消息数: {summary['total_messages']}\")\n", "                        print(f\"- 库存物品: {rag_summary['inventory_items']}\")\n", "                        print(f\"- 金币: {rag_summary['gold']}\")\n", "                        print(f\"- 关键事件: {rag_summary['key_events']}\")\n", "                        continue\n", "                    elif not user_input:\n", "                        print(\"❌ 请输入您的行动！\")\n", "                        continue\n", "                    \n", "                    # 处理用户输入\n", "                    session_manager.add_message(\"user\", user_input)\n", "                    rag_manager.analyze_message_for_updates(\"user\", user_input)\n", "                    \n", "                    # 获取AI回应\n", "                    messages = session_manager.get_messages_for_model()\n", "                    rag_context = rag_manager.get_context_for_ai()\n", "                    \n", "                    if rag_context:\n", "                        enhanced_content = f\"{user_input}\\n\\n游戏状态:\\n{rag_context}\"\n", "                        messages[-1][\"content\"] = enhanced_content\n", "                    \n", "                    response = model_manager.generate_response(messages)\n", "                    session_manager.add_message(\"assistant\", response)\n", "                    rag_manager.analyze_message_for_updates(\"assistant\", response)\n", "                    \n", "                    print(f\"\\n🎭 游戏主持人: {response}\")\n", "                    \n", "                except KeyboardInterrupt:\n", "                    print(\"\\n👋 游戏被中断，再见！\")\n", "                    break\n", "                except Exception as e:\n", "                    print(f\"❌ 处理消息时出错: {str(e)}\")\n", "        \n", "        simple_chat()\n", "else:\n", "    print(\"❌ 请先加载模型！\")\n", "    print(\"请运行上方的'加载AI模型'单元格\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}