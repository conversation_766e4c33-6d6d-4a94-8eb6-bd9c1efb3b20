# 客户需求理解

## 项目背景
- 客户：Kodaav
- 项目：AI游戏主持人（单人桌面RPG）
- 这是4阶段项目的第0阶段
- 目标：测试AI游戏主持人的早期版本
- 后续计划：模型微调、高级GUI开发
- 游戏类型：基于Elder Scrolls世界观的高幻想RPG
- 游戏风格：神话与严酷并存的叙事风格

## 技术环境
- 平台：Google Colab
- 硬件：A100 GPU服务器
- 模型：Llama 70B Instruct (LoRA微调版本)
- 模型文件：.safetensors格式
- 客户有Colab+订阅和Hugging Face访问权限

## 第一阶段核心功能需求

### 1. 模型加载与运行
- 从Google Drive复制.safetensors文件到Colab本地存储
- 确保模型在GPU上运行，避免CPU加载
- 支持Llama 70B Instruct (LoRA微调版本)
- 优化GPU内存使用

### 2. 聊天界面设计
- 类似ChatGPT的界面风格
- 使用Abezee字体（Google开放字体）
- 品牌配色：黑色 + #107c10（Xbox绿色）
- 支持完整对话历史显示和滚动
- 简单易用的文本输入框

### 3. 数据导入导出功能
- **JSON格式**：
  - 用于数据集处理
  - 典型的用户/助手对话结构
  - 客户已提供示例JSON格式（Ursus Werebear Awakens.json）
  - 包含完整的RPG游戏会话记录
  - 支持复杂的游戏状态和角色互动
- **DOCX格式**：
  - 用于后续编辑和叙述创建
  - 支持格式调整
  - 便于会话记录整理

### 4. 会话管理
- 保存当前对话会话
- 加载历史对话会话
- 导出会话为指定格式
- 会话数据持久化存储

### 5. 代码架构要求
- 模块化设计（符合Colab笔记本特点）
- 便于后续功能扩展
- 清晰的代码结构
- 易于维护和升级

## 第二阶段RAG功能需求

### 1. 文档管理系统
需要管理三个核心文档：
- **库存清单 (Inventory)**：跟踪玩家物品
- **关键事件 (Key Events)**：记录重要故事节点
- **玩家偏好 (Player Preferences)**：存储玩家选择倾向

### 2. 自动更新机制
- 模型根据会话内容自动更新三个文档
- 实时跟踪故事发展和玩家行为
- 智能识别需要记录的信息

### 3. 文档操作功能
- 下载文档到本地
- 导入历史文档
- 跨会话数据持久化
- 文档版本管理

## 项目约束与要求

### 技术约束
- 必须在Google Colab环境运行
- 确保GPU资源有效利用
- 兼容A100服务器配置
- 处理大模型内存需求

### 用户体验要求
- 界面美观，符合品牌风格
- 操作简单直观
- 响应速度快
- 稳定可靠

### 扩展性要求
- 为后续功能预留接口
- 支持模型微调工作流
- 便于集成更多RPG功能
- 模块化架构便于维护

## 交付物
1. 完整的Colab笔记本脚本
2. 详细的使用说明文档
3. 代码注释和架构说明
4. 测试用例和示例

## JSON数据格式分析
基于客户提供的示例文件"Ursus Werebear Awakens.json"：

### 对话结构
- 每条消息包含`role`和`content`字段
- `role`值：`"user"`（玩家）或`"assistant"`（AI游戏主持人）
- `content`：完整的游戏对话内容

### 游戏特色
- 支持复杂的RPG机制（战斗系统、技能检定、物品管理）
- 包含丰富的世界观设定（Elder Scrolls宇宙）
- 支持角色扮演和沉浸式叙事
- 包含选择分支和后果系统

### 技术要求
- 需要处理长篇对话（示例文件包含200+轮对话）
- 支持游戏状态跟踪和连续性
- 需要维护角色信息和世界状态

## 预算与时间
- 第一阶段：$80 USD
- 第二阶段RAG功能：免费包含
- 预计交付时间：3天
- 包含2次修订机会
