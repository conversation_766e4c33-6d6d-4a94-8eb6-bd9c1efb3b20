# 测试Gradio版本兼容性
try:
    import gradio as gr
    print(f"✅ Gradio版本: {gr.__version__}")
    
    # 测试主题
    try:
        theme = gr.themes.Dark()
        print("✅ 支持 gr.themes.Dark()")
    except AttributeError:
        print("❌ 不支持 gr.themes.Dark()")
        try:
            theme = "dark"
            print("✅ 支持字符串主题 'dark'")
        except:
            print("❌ 不支持字符串主题")
            theme = None
            print("✅ 使用默认主题")
    
    # 测试简单界面
    def test_interface():
        with gr.Blocks(title="测试", theme=theme) as demo:
            gr.Markdown("# 测试界面")
            textbox = gr.Textbox(label="输入")
            button = gr.Button("提交")
            output = gr.Textbox(label="输出")
            
            def process(text):
                return f"您输入了: {text}"
            
            button.click(process, inputs=textbox, outputs=output)
        
        return demo
    
    demo = test_interface()
    print("✅ Gradio界面创建成功")
    
except ImportError:
    print("❌ Gradio未安装")
except Exception as e:
    print(f"❌ Gradio测试失败: {str(e)}")
